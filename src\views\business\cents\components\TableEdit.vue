<template>
  <div class="template-content">
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
      <div v-if="specs.length > 0" class="specs-content">
        <div v-for="(item, index) in specs" :key="index" class="specs-li">
          <el-card>
            <div slot="header" class="header-content">
              <div class="header-left">
                <span>分账模板名称：</span>
                <el-input
                  v-model="item.title"
                  clearable
                  placeholder="请输入分账模板名称"
                  style="width: 200px"
                />
              </div>
              <div class="header-right"></div>
            </div>
            <div class="value-content">
              <div v-for="(v, i) in item.value" :key="i" class="value-li">
                <span class="label">分账商户：</span>
                <el-select
                  v-model="v.merchantId"
                  filterable
                  placeholder="请选择分账商户"
                  style="width: 800px"
                >
                  <el-option
                    v-for="it in storeLists"
                    :key="it.id"
                    :label="it.name"
                    :value="it.id"
                  />
                </el-select>
                <span class="label">分账比例：</span>
                <el-input
                  v-model="v.ratio"
                  clearable
                  placeholder="请输入分账比例"
                  type="number"
                />
                &nbsp;%&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <el-checkbox v-model="v.ifCharge" :true-label="1" :false-label="0" @change="handleFees(i)">
                  承担手续费
                </el-checkbox>
                <el-button
                  circle
                  class="delete-value"
                  icon="el-icon-delete"
                  size="mini"
                  type="danger"
                  @click="deleteValue(index, i)"
                />
              </div>
              <el-button
                class="add-specs-value"
                icon="el-icon-plus"
                plain
                @click="addSpecsValue(index)"
              >
                添加商户
              </el-button>
            </div>
          </el-card>
        </div>
      </div>
      <div class="bottom-button">
        <el-button type="info" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handAdds">确定修改</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import { editTemplate, editBatch } from '@/api/business'
  import { getAllMerchantIdsAndNames } from '@/api/device'
  export default {
    name: 'TableAdd',
    data() {
      return {
        dialogTitle: '修改分账模板', // 弹窗标题
        dialogVisible: false, // 弹窗状态
        id: '', // 模板id
        specs: [], // 分账模板规格
        storeLists: [], // 所有商户列表
      }
    },
    created() {},
    methods: {
      // 选中商家
      selectMerchant(merchantId, index, i) {
        console.log(merchantId, index, i)
      },
      // 所有商户下拉列表
      async storeallLists() {
        let { code, data } = await getAllMerchantIdsAndNames({})
        if (code == '00000') {
          this.storeLists = data
        }
      },
      // 删除属性
      deleteSpecs(index) {
        this.specs.splice(index, 1)
      },
      // 添加属性值
      addSpecsValue(index) {
        let obj = { merchantId: '', ratio: '', ifCharge: 0 }
        this.specs[index].value.push(obj)
      },
      // 删除属性值
      deleteValue(index, i) {
        this.specs[index].value.splice(i, 1)
      },
      // 手续费
      handleFees(i) {
        let arr = this.specs[0].value
        arr.forEach((item, index) => {
          if (i == index) {
            item.ifCharge = item.ifCharge == 1? 1 : 0
          } else {
            item.ifCharge = 0
          }
        })
        this.specs[0].value = arr
      },
      // 显示弹窗
      show(e) {
        // 获取所有商户
        this.storeallLists()
        // 新增模板
        let obj = {
          title: e.name,
          value: e.details,
        }
        this.specs = [obj]
        this.id = e.id
        this.dialogVisible = true
      },
      // 新增模板
      async handAdds() {
        let flag = true
        let ratioNum = 0
        let heavy = []
        if (this.specs.length > 0) {
          let arr = this.specs
          for (let i = 0; i < arr.length; i++) {
            if (arr[i].title === '') {
              this.$notify.error({
                title: '温馨提示',
                message: `第${i + 1}条模板名称不能为空`,
                offset: 180,
              })
              flag = false
              break
            }
            for (let j = 0; j < arr[i].value.length; j++) {
              if (arr[i].value[j].merchantId === '') {
                this.$notify.error({
                  title: '温馨提示',
                  message: `${arr[i].title}模板中第${j + 1}条商户不能为空`,
                  offset: 180,
                })
                flag = false
                break
              }
              if (arr[i].value[j].ratio === '') {
                this.$notify.error({
                  title: '温馨提示',
                  message: `${arr[i].title}规格中第${j + 1}条比例不能为空`,
                  offset: 180,
                })
                flag = false
                break
              }
              ratioNum += Number(arr[i].value[j].ratio)
            }
          }
          const checkForDuplicates = (arr) => new Set(arr).size === arr.length
          if (flag && !checkForDuplicates(heavy)) {
            this.$notify.error({
              title: '温馨提示',
              message: `模板中存在重复商户，请重新选择`,
              offset: 180,
            })
            flag = false
          }
          if (flag && ratioNum > 100) {
            this.$notify.error({
              title: '温馨提示',
              message: `模板比例之和不能大于100`,
              offset: 180,
            })
            flag = false
          }
        }
        if (!flag) return false
        let { code } = await editTemplate({
          id: this.id,
          name: this.specs[0].title,
        })
        if (code === '00000') {
          // 修改模板内容
          this.sureAddBatch()
        }
      },
      // 修改模板内容
      async sureAddBatch() {
        let { code, data } = await editBatch({
          templateId: this.id,
          details: this.specs[0].value,
        })
        if (code === '00000' && data) {
          this.$notify.success({
            title: '温馨提示',
            message: '修改成功',
            offset: 180,
          })
          this.dialogVisible = false
          this.$emit('fetch-data')
        }
      },
    },
  }
</script>
<style lang="scss" scoped>
  .template-content {
    width: 100%;
    .specs-content {
      display: flex;
      flex-wrap: wrap;

      .specs-li {
        width: 100%;

        .header-content {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .header-left {
            display: flex;
            align-items: center;
          }
        }

        .value-content {
          display: flex;
          flex-direction: column;

          .value-li {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 5px 0px;
            .label {
              width: 280px;
              text-align: center;
            }
            .delete-value {
              margin-left: 15px;
            }
          }

          .add-specs-value {
            margin-top: 10px;
          }
        }
      }
    }
  }
  .bottom-button {
    margin: 20px auto;
    display: flex;
    justify-content: center;
  }
</style>
