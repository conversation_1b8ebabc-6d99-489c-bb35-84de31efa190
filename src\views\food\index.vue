<template>
  <div class="index-container">
    <!-- 查询条件 -->
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form :inline="true" label-width="80px">
          <el-form-item label="快捷操作：">
            <el-button
              icon="el-icon-circle-plus-outline"
              type="primary"
              @click="handleAdd"
            >
              添加餐品
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <!-- 按钮 -->
    </vab-query-form>
    <!-- begin表格 -->
    <el-table ref="tableSort" v-loading="loading" border :data="tableData">
      <div slot="empty" style="margin: 50px 0">
        <img
          class="img"
          src="https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/image/1865665326720536578.png"
        />
      </div>
      <!-- <el-table-column align="center" label="序号">
        <template #default="scope">
          {{
            (queryForm.currentPage - 1) * queryForm.pageSize + scope.$index + 1
          }}
        </template>
      </el-table-column> -->
      <el-table-column align="center" label="ID">
        <template #default="{ row }">
          {{ row.id }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="餐品名称">
        <template #default="{ row }">
          <div>{{ row.name }}</div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="餐品图片" width="230">
        <template #default="{ row }">
          <el-image
            :preview-src-list="[row.imageUrl]"
            :src="row.imageUrl"
            style="width: 200px; height: 120px"
          >
            <div slot="error" class="image-slot-error">
              <i class="el-icon-picture-outline" />
            </div>
          </el-image>
        </template>
      </el-table-column>
      <!-- <el-table-column align="center" label="餐品价格">
        <template #default="{ row }">
          <div>{{ row.price }}</div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="餐品库存">
        <template #default="{ row }">
          <div>{{ row.stock }}</div>
        </template>
      </el-table-column> -->
      <el-table-column align="center" label="餐品类型">
        <template #default="{ row }">
          <div>{{ row.categoryName }}</div>
        </template>
      </el-table-column>
      <!-- <el-table-column align="center" label="餐品编号">
        <template #default="{ row }">
          <div>{{ row.code }}</div>
        </template>
      </el-table-column> -->
      <el-table-column align="center" fixed="right" label="操作" width="150">
        <template #default="{ row }">
          <div>
            <el-link type="primary" @click="handleEdit(row)">编辑</el-link>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="queryForm.pageIndex"
      :layout="layout"
      :page-size="queryForm.pageSize"
      :total="queryForm.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <!-- end表格 -->
  </div>
</template>

<script>
  import { pageList } from '@/api/food'
  export default {
    name: 'GoodsIndex',
    components: {},
    data() {
      return {
        queryForm: {
          currentPage: 1, // 页码
          pageSize: 10, // 页数
          operType: 0, // 0查询，1导出
          total: 0, // 总条数
        },
        loading: false, // 加载状态
        tableData: [], // 列表数据
        layout: 'total, sizes, prev, pager, next, jumper', //   分页布局
      }
    },
    // 在组件创建时执行的钩子函数
    created() {
      // 使用$nextTick确保在DOM更新后执行回调函数
      this.$nextTick(() => {
        // 调用fetchData方法获取数据
        this.fetchData()
      })
    },
    methods: {
      // 查询
      handleQuery() {
        this.queryForm.currentPage = 1
        this.fetchData()
      },
      // 分页
      handleSizeChange(val) {
        this.queryForm.currentPage = val
        this.fetchData()
      },
      // 分页
      handleCurrentChange(val) {
        this.queryForm.currentPage = val
        this.fetchData()
      },
      // 列表查询
      async fetchData() {
        this.loading = true
        const { code, data } = await pageList(this.queryForm)
        if (code == '00000') {
          this.tableData = data.data
          this.queryForm.total = data.paginator.totalRecord
          this.loading = false
        }
      },
      // 添加餐品
      handleAdd() {
        this.$router.push({
          path: '/food/add',
        })
      },
      // 编辑商品
      handleEdit(row) {
        this.$router.push({
          path: '/food/edit',
          query: { id: row.id },
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .link-padding {
    margin-left: 15px;
  }
  .table-column {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .table-column-price {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
</style>
<style>
  .image-slot-error {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f2f6fc;
    font-size: 26px;
  }
</style>
