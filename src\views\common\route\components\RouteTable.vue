<template>
  <el-drawer :direction="direction" size="1100px" :title="title" :visible.sync="dialogFormVisible">
    <div style="padding: 0px 50px 0 20px">
      <el-form ref="form" label-width="160px" :model="form">
        <el-form-item label="上级类目" required>
          <tree-select v-model="form.pid" :default-expand-level="2" no-children-text="没有数据了" :normalizer="normalizer"
            :options="routesData" placeholder="选择上级菜单" :show-count="true" />
        </el-form-item>
        <el-form-item label="菜单名称" required>
          <el-input v-model="form.title" />
        </el-form-item>
        <el-form-item label="菜单图标" required>
          <el-popover popper-class="icon-selector-popper" trigger="hover" width="800">
            <template #reference>
              <el-button>
                <vab-icon :icon="form.icon" size="30" />
                图标选择器
                <vab-icon icon="arrow-down-s-line" />
              </el-button>
              <span style="padding-left: 20px"></span>
              <vab-icon :icon="form.icon" size="40" />
              <!-- {{ form.icon }} -->
            </template>
            <vab-icon-selector @handle-icon="handleIcon" />
          </el-popover>
        </el-form-item>
        <el-form-item label="路由地址(path)" required>
          <el-input v-model="form.path" />
          <div class="shili">
            <p>示例：一级菜单填写：/index</p>
            <p>二级或二级菜单以下填写 view (等同于 /index/view)</p>
          </div>
        </el-form-item>
        <el-form-item label="路由名称(name)" required>
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="组件地址component" required>
          <el-input v-model="form.component" />
          <div class="shili">
            <p>示例：一级菜单填写：Layout</p>
            <p>二级或二级菜单以下 @/views/index</p>
          </div>
        </el-form-item>
        <el-form-item label="排序序号" required>
          <el-input v-model="form.sort" type="number" />
        </el-form-item>
        <el-form-item label="badge">
          <el-input v-model="form.badge" />
        </el-form-item>
        <el-form-item label="">
          <el-row>
            <el-col :span="12">
              面包屑是否隐藏
              <el-radio-group v-model="form.breadcrumbHidden">
                <el-radio border :label="true">true</el-radio>
                <el-radio border :label="false">false</el-radio>
              </el-radio-group>
            </el-col>
            <el-col :span="12">
              是否显示关闭按钮
              <el-radio-group v-model="form.noClosable">
                <el-radio border :label="true">true</el-radio>
                <el-radio border :label="false">false</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row style="padding-top: 12px">
            <el-col :span="12">
              无分栏noColumn
              <el-radio-group v-model="form.noColumn">
                <el-radio border :label="true">true</el-radio>
                <el-radio border :label="false">false</el-radio>
              </el-radio-group>
            </el-col>
            <el-col :span="12">
              菜单是否显示（适用详情编辑）
              <el-radio-group v-model="form.hidden" @change="changeHidden">
                <el-radio border :label="true">显示</el-radio>
                <el-radio border :label="false">隐藏</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <div v-if="!form.hidden" style="padding: 10px 0px 0px 0px">
            <el-input v-model="form.activeMenu" placeholder="请输入高亮的父级菜单路由地址" />
            <div class="shili">
              <p>
                示例：父级的路由地址为：/vab/table/detail6 则填写
                /vab/table/detail6
              </p>
            </div>
          </div>
        </el-form-item>
        <el-form-item v-if="type == 2" label="状态">
          <el-radio-group v-model="form.state">
            <el-radio border :label="0">正常</el-radio>
            <el-radio border :label="1">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div style="text-align: center">
        <el-button type="primary" @click="save">确 定</el-button>
        <el-button @click="close">取 消</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { getList, doRouteAdd, doRouteUpdate } from '@/api/router'
import TreeSelect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import VabIconSelector from './VabIconSelector'
export default {
  name: 'RouteTable',
  components: { TreeSelect, VabIconSelector },
  props: {},
  data() {
    return {
      type: 1, //1添加菜单  2编辑菜单  reduceMotionChanged
      form: {
        pid: null, // 上级菜单ID，第一级传0
        title: '', // 菜单名称
        icon: 'book-2-fill', // 菜单图标
        badge: '', // 标识
        path: '', // 路由地址
        name: '', // 路由名称
        component: '', // 组件地址
        sort: 1, // 排序序号
        isBlank: false, // 是否外部链接
        type: 1, // 菜单类别，1代表菜单，2代表按钮
        breadcrumbHidden: false, // 面包屑是否隐藏
        noClosable: false, // tabs显示关闭按钮
        noColumn: false, // 是否不显示栏目
        hidden: true, // 菜单是否显示
        activeMenu: '', // 如果隐藏菜单后，写父级路由地址
        dynamicNewTab: false, // 动态新建tab
      },
      queryForm: {
        currentPage: 1, // 页码
        pageSize: 200, // 页数
        operType: 0, // 0查询，1导出
      },
      title: '', //子菜单标题
      clickModel: false,
      direction: 'ltr',
      dialogFormVisible: false,
      routesData: [], //上级菜单
    }
  },
  watch: {},
  created() { },
  methods: {
    // 获取当前菜单列表
    async requestRoleList() {
      let { data } = await getList(this.queryForm)
      this.routesData = [{ id: 0, name: '主类目菜单', children: data }]
    },
    //** 转换菜单数据结构 */
    normalizer(node) {
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      }
    },
    showEdit(row) {
      if (!row) {
        this.title = '添加菜单'
        this.type = 1
        this.form.pid = null // 上级菜单ID，第一级传0
        this.form.title = '' // 菜单名称
        this.form.icon = 'book-2-fill' // 菜单图标
        this.form.badge = '' // 标识
        this.form.path = '' // 路由地址
        this.form.name = '' // 路由名称
        this.form.component = '' // 组件地址
        this.form.sort = 1 // 排序序号
        this.form.breadcrumbHidden = false // 面包屑是否隐藏
        this.form.noClosable = false // tabs显示关闭按钮
        this.form.noColumn = false // 是否不显示栏目
        this.form.hidden = true // 菜单是否显示
        this.form.activeMenu = '' // 如果隐藏菜单后，写父级路由地址
        this.form.dynamicNewTab = false // 动态新建tab
      } else {
        this.title = '编辑菜单'
        this.type = 2
        let obj = Object.assign({}, row)
        this.form.id = obj.id
        this.form.pid = obj.pid
        this.form.title = obj.title
        this.form.icon = obj.icon
        this.form.badge = obj.badge
        this.form.path = obj.path
        this.form.name = obj.name
        this.form.component = obj.component
        this.form.sort = obj.sort
        this.form.state = 0
        this.form.breadcrumbHidden = obj.breadcrumbHidden
        this.form.noClosable = obj.noClosable
        this.form.noColumn = obj.noColumn
        this.form.hidden = obj.hidden == 1 ? true : false
        this.form.activeMenu = ''
        this.form.dynamicNewTab = obj.dynamicNewTab
      }
      this.requestRoleList()
      //显示 dialog
      this.dialogFormVisible = true
    },
    close() {
      this.$refs['form'].resetFields()
      this.dialogFormVisible = false
    },
    //选择后的图标
    handleIcon(item) {
      this.form.icon = item
    },
    //选择菜单是否隐藏
    changeHidden(event) {
      if (!event) {
        this.form.dynamicNewTab = true
      } else {
        this.form.activeMenu = ''
        this.form.dynamicNewTab = false
      }
    },
    async save() {
      if (typeof this.form.pid == 'object') {
        this.$notify.error({
          title: '温馨提示',
          message: '请选择上级类目',
          offset: 200,
        })
        return false
      } else if (this.form.title == '') {
        this.$notify.error({
          title: '温馨提示',
          message: '请输入菜单名称',
          offset: 200,
        })
        return false
      } else if (this.form.path == '') {
        this.$notify.error({
          title: '温馨提示',
          message: '请输入路由地址(path)',
          offset: 200,
        })
        return false
      } else if (this.form.name == '') {
        this.$notify.error({
          title: '温馨提示',
          message: '请输入路由名称(name)',
          offset: 200,
        })
        return false
      } else if (this.form.component == '') {
        this.$notify.error({
          title: '温馨提示',
          message: '请输入组件地址component',
          offset: 200,
        })
        return false
      } else if (this.form.sort == '') {
        this.$notify.error({
          title: '温馨提示',
          message: '请输入排序序号，为数字整型，最小为1',
          offset: 200,
        })
        return false
      } else {
        if (this.type == 1) {
          const { msg } = await doRouteAdd(this.form)
          this.$baseMessage(msg, 'success', 'vab-hey-message-success')
        } else {
          const { msg } = await doRouteUpdate(this.form)
          this.$baseMessage(msg, 'success', 'vab-hey-message-success')
        }
        this.$emit('fetch-data')
        this.close()
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.image-box {
  .image {
    width: 45px;
    height: 45px;
    margin-right: 10px;
    margin-bottom: 10px;
    cursor: pointer;
    border-radius: 100%;
  }

  .active {
    width: 48px;
    height: 48px;
    border: 3px solid #f34d37;
  }
}

.shili {
  padding: 0px 15px;
  margin: 6px 0px 0px 0px;
  background-color: rgb(255, 246, 247);
  border-left: 5px solid rgb(254, 108, 111);
  border-radius: 4px;

  p {
    padding: 0;
    margin: 0;
    font-size: 10px;
    line-height: 20px;
    color: rgb(94, 109, 130);
  }
}
</style>
