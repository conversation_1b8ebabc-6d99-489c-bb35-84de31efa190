<template>
  <div class="index-container">
    <el-drawer
      :before-close="handleClose"
      size="80%"
      :title="drawerTitle"
      :visible.sync="showDrawer"
      :wrapperClosable="false"
    >
      <div style="margin: 10px auto 30px">
        <el-steps :active="active" align-center finish-status="success">
          <el-step
            v-for="(item, index) in stepList"
            :key="index"
            :description="item.description"
            :title="item.description"
          />
        </el-steps>
      </div>
      <div style="padding: 0 70px">
        <el-form
          ref="form"
          class="demo-form"
          label-position="right"
          label-width="170px"
          :model="queryForm"
          :rules="rules"
        >
          <el-row :gutter="0">
            <div v-if="active === 0">
              <el-col :span="24">
                <el-form-item label="主体类型" required>
                  <el-select
                    v-model="queryForm.merchantType"
                    placeholder="请选择主体类型"
                    style="width: 100%"
                    @change="changeEntityType"
                  >
                    <el-option
                      v-for="item in entityTypeList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="queryForm.merchantType == '2'" :span="24">
                <el-form-item
                  label="是否个人独资企业"
                  prop="isSoleProprietorship"
                >
                  <el-radio-group v-model="queryForm.soleInvestor">
                    <el-radio :label="'0'">否</el-radio>
                    <el-radio :label="'1'">是</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="isEnterprise? '商户名称' : '商户名称(个人名称)'" prop="merchantName">
                  <el-input
                    v-model="queryForm.merchantName"
                    placeholder="请输入商户名称"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="isEnterprise? '商户简称' : '商户简称(个人简称)'" prop="shortName">
                  <el-input
                    v-model="queryForm.shortName"
                    placeholder="请输入商户简称"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="姓名" prop="contactPerson">
                  <el-input
                    v-model="queryForm.contactPerson"
                    placeholder="请输入联系人姓名"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="手机号码" prop="contactPhone">
                  <el-input
                    v-model="queryForm.contactPhone"
                    placeholder="请输入联系人手机号码"
                    type="number"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24" v-if="isEnterprise">
                <el-form-item label="身份类型" prop="contactType">
                  <el-radio-group disabled v-model="queryForm.contactType">
                    <el-radio :label="'1'">法人</el-radio>
                    <el-radio :label="'0'">经办人</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </div>
            <!-- 企业用户 -->
            <div v-if="isEnterprise">
              <!-- 经营信息 -->
              <div v-if="active === 1">
                <el-col :span="24">
                  <el-form-item label="邮箱地址" prop="email">
                    <el-input
                      v-model="queryForm.email"
                      placeholder="请输入邮箱地址"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="省市区" required>
                    <el-cascader
                      v-model="areaValue"
                      :options="areaData"
                      placeholder="请选择省市区"
                      :props="{
                        value: 'areaCode',
                        label: 'areaName',
                        children: 'children',
                      }"
                      style="width: 100%"
                      @change="onChangeArea"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item :label="isEnterprise? '商户类别' : '(个人类别)'" prop="mcc">
                    <el-select
                      v-model="queryForm.mcc"
                      filterable
                      placeholder="请输入商户类别，例如：台球、洗浴、KTV、咖啡馆等"
                      remote
                      :remote-method="getMccList"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="(item, index) in mccList"
                        :key="index"
                        :label="item.name"
                        :value="item.code"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="营业执照图片" required>
                    <div v-if="queryForm.businessLicensePhoto != ''">
                      <ul class="upload-img-box">
                        <li class="upload-img">
                          <el-image
                            class="img"
                            :preview-src-list="[queryForm.businessLicensePhoto]"
                            :src="queryForm.businessLicensePhoto"
                            style="width: 100%; height: 100%"
                          />
                          <img
                            class="close"
                            src="~@/assets/index_images/close_icon.png"
                            @click="removeImgs(18)"
                          />
                        </li>
                      </ul>
                    </div>
                    <div v-else>
                      <img-upload
                        :tips="imgTips"
                        @upload-data="getBusinessLicensePhoto"
                      />
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="营业执照名称" prop="businessLicenseName">
                    <el-input
                      v-model="queryForm.businessLicenseName"
                      placeholder="请输入营业执照名称"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="营业执照编号" prop="businessLicenseCode">
                    <el-input
                      v-model="queryForm.businessLicenseCode"
                      placeholder="请输入营业执照编号"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="执照注册地址" prop="registerAddress">
                    <el-input
                      v-model="queryForm.registerAddress"
                      placeholder="请输入营业执照注册地址"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="经营详细地址" prop="businessAddress">
                    <el-input
                      v-model="queryForm.businessAddress"
                      placeholder="请输入经营详细地址"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="经营范围" prop="businessScope">
                    <el-input
                      v-model="queryForm.businessScope"
                      placeholder="请输入经营范围"
                      type="textarea"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="注册资本" prop="registeredCapital">
                    <el-input
                      v-model="queryForm.registeredCapital"
                      placeholder="请输入注册资本"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="执照有效期（起始）" prop="businessLicenseFrom">
                    <el-date-picker
                      v-model="queryForm.businessLicenseFrom"
                      placeholder="请选择营业执照起始有效期"
                      type="date"
                      value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="执照有效期（截止）" prop="businessLicenseTo">
                    <el-date-picker
                      v-if="!businessLicenseToLongTerm"
                      v-model="queryForm.businessLicenseTo"
                      placeholder="请选择营业执照截止有效期"
                      style="margin-right: 40px"
                      type="date"
                      value-format="yyyy-MM-dd"
                    />
                    <el-checkbox
                      v-model="businessLicenseToLongTerm"
                      @change="businessLicenseToLongTermChange"
                    >
                      长期
                    </el-checkbox>
                  </el-form-item>
                </el-col>
              </div>
              <!-- 法人身份信息 -->
              <div v-if="active === 2">
                <el-col :span="24">
                  <el-form-item label="证件类型" prop="lawyerCertType">
                    <el-select
                      v-model="queryForm.lawyerCertType"
                      placeholder="请选择法人证件类型"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in identityList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="证件号码" prop="lawyerCertNo">
                    <el-input
                      v-model="queryForm.lawyerCertNo"
                      placeholder="请输入所选择的证件号码"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="证件正面照" required>
                    <div v-if="queryForm.lawyerCertPhotoFront != ''">
                      <ul class="upload-img-box">
                        <li class="upload-img">
                          <el-image
                            class="img"
                            :preview-src-list="[queryForm.lawyerCertPhotoFront]"
                            :src="queryForm.lawyerCertPhotoFront"
                            style="width: 100%; height: 100%"
                          />
                          <img
                            class="close"
                            src="~@/assets/index_images/close_icon.png"
                            @click="removeImgs(4)"
                          />
                        </li>
                      </ul>
                    </div>
                    <div v-else>
                      <img-upload
                        :tips="imgTips"
                        @upload-data="getLegalIdFrontPhoto"
                      />
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="证件背面照" required>
                    <div v-if="queryForm.lawyerCertPhotoBack != ''">
                      <ul class="upload-img-box">
                        <li class="upload-img">
                          <el-image
                            class="img"
                            :preview-src-list="[queryForm.lawyerCertPhotoBack]"
                            :src="queryForm.lawyerCertPhotoBack"
                            style="width: 100%; height: 100%"
                          />
                          <img
                            class="close"
                            src="~@/assets/index_images/close_icon.png"
                            @click="removeImgs(5)"
                          />
                        </li>
                      </ul>
                    </div>
                    <div v-else>
                      <img-upload
                        :tips="imgTips"
                        @upload-data="getLegalIdBackPhoto"
                      />
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="证件人姓名" prop="lawyerName">
                    <el-input
                      v-model="queryForm.lawyerName"
                      @input="inLawyerName"
                      placeholder="请输入证件人姓名"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="证件有效期（起始）" prop="certificateFrom">
                    <el-date-picker
                      v-model="queryForm.certificateFrom"
                      placeholder="请选择证件起始有效期"
                      type="date"
                      value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="证件有效期（截止）" prop="certificateTo">
                    <el-date-picker
                      v-if="!certificateToLongTerm"
                      v-model="queryForm.certificateTo"
                      placeholder="请选择证件截止有效期"
                      style="margin-right: 40px"
                      type="date"
                      value-format="yyyy-MM-dd"
                    />
                    <el-checkbox
                      v-model="certificateToLongTerm"
                      @change="certificateToLongTermChange"
                    >
                      长期
                    </el-checkbox>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="法人手机号码" prop="legalPersonPhone">
                    <el-input
                      v-model="queryForm.legalPersonPhone"
                      placeholder="请输入法人手机号码"
                      type="number"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="法人证件地址（国籍）" prop="lawyerCountry">
                    <el-select
                      v-model="queryForm.lawyerCountry"
                      filterable
                      placeholder="请选择法人证件地址（国籍）"
                      remote
                      :remote-method="getCountryList"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in nationList"
                        :key="item.id"
                        :label="item.countryName"
                        :value="item.countryCode"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="法人证件详情地址" prop="lawyerAddress">
                    <el-input
                      v-model="queryForm.lawyerAddress"
                      placeholder="请输入法人证件详情地址"
                    />
                  </el-form-item>
                </el-col>
              </div>
              <!-- 注册账户信息 -->
              <div v-if="active === 3">
                <el-col :span="24">
                  <el-form-item label="账户类型" prop="openAccountType" required>
                    <el-radio-group v-model="queryForm.openAccountType" disabled>
                      <el-radio :label="'1'">对公账户</el-radio>
                      <el-radio :label="'2'">法人账户</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="账户名称" prop="licenceAccount">
                    <el-input
                      v-model="queryForm.licenceAccount"
                      placeholder="请输入账户名称"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="账户账号" prop="licenceAccountNo">
                    <el-input
                      v-model="queryForm.licenceAccountNo"
                      placeholder="请输入账户账号"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="开户银行" prop="licenceOpenBank">
                    <el-select
                      v-model="queryForm.licenceOpenBank"
                      filterable
                      placeholder="请输入开户银行"
                      remote
                      :remote-method="querySearch"
                      style="width: 100%"
                      @change="bankBranchSearch"
                    >
                      <el-option
                        v-for="item in bankData"
                        :key="item.id"
                        :label="item.bankName"
                        :value="item.bankName"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="开户支行" prop="licenceOpenSubBank">
                    <el-select
                      v-model="queryForm.licenceOpenSubBank"
                      filterable
                      placeholder="请输入开户银行"
                      remote
                      :remote-method="bankBranchSearch"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in subBranchList"
                        :key="item.id"
                        :label="item.bankName"
                        :value="item.bankName"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="证明文件(开户许可证)" required>
                    <div v-if="queryForm.openingLicenseAccountPhoto != ''">
                      <ul class="upload-img-box">
                        <li class="upload-img">
                          <el-image
                            class="img"
                            :preview-src-list="[
                              queryForm.openingLicenseAccountPhoto,
                            ]"
                            :src="queryForm.openingLicenseAccountPhoto"
                            style="width: 100%; height: 100%"
                          />
                          <img
                            class="close"
                            src="~@/assets/index_images/close_icon.png"
                            @click="removeImgs(6)"
                          />
                        </li>
                      </ul>
                    </div>
                    <div v-else>
                      <img-upload :tips="imgTips" @upload-data="getOpeningPermit" />
                    </div>
                  </el-form-item>
                </el-col>
              </div>
              <!-- 结算账户信息 -->
              <div v-if="active === 4">
                <el-col :span="24">
                  <el-form-item label="结算方式" prop="settleTarget">
                    <el-radio-group v-model="queryForm.settleTarget" disabled>
                      <el-radio :label="'1'">自动提现</el-radio>
                      <el-radio :label="'2'">手动提现</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="结算账户类型" prop="settleAccountType">
                    <el-radio-group v-model="queryForm.settleAccountType">
                      <el-radio :label="'1'">对公账户</el-radio>
                      <el-radio :label="'2'">法人账户</el-radio>
                      <el-radio disabled :label="'3'">授权对公</el-radio>
                      <el-radio disabled :label="'4'">授权对私</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="结算账户账号" prop="settleAccountNo">
                    <el-input
                      v-model="queryForm.settleAccountNo"
                      placeholder="请输入结算账户账号"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="结算账户名称" prop="settleAccount">
                    <el-input
                      v-model="queryForm.settleAccount"
                      placeholder="请输入结算账户名称"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="结算开户银行" prop="openBank">
                    <el-select
                      v-model="queryForm.openBank"
                      filterable
                      placeholder="请输入开户银行"
                      remote
                      :remote-method="querySearch"
                      style="width: 100%"
                      @change="bankBranchSearch"
                    >
                      <el-option
                        v-for="item in bankData"
                        :key="item.id"
                        :label="item.bankName"
                        :value="item.bankName"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="结算开户支行" prop="openSubBank">
                    <el-select
                      v-model="queryForm.openSubBank"
                      filterable
                      placeholder="请输入开户银行"
                      remote
                      :remote-method="bankBranchSearch"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in subBranchList"
                        :key="item.id"
                        :label="item.bankName"
                        :value="item.bankName"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="开户联行号" prop="openBankCode">
                    <el-input
                      v-model="queryForm.openBankCode"
                      placeholder="请输入开户联行号"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="结算人身份证正面">
                    <div v-if="queryForm.settleCertFrontPhoto != ''">
                      <ul class="upload-img-box">
                        <li class="upload-img">
                          <el-image
                            class="img"
                            :preview-src-list="[queryForm.settleCertFrontPhoto]"
                            :src="queryForm.settleCertFrontPhoto"
                            style="width: 100%; height: 100%"
                          />
                          <img
                            class="close"
                            src="~@/assets/index_images/close_icon.png"
                            @click="removeImgs(7)"
                          />
                        </li>
                      </ul>
                    </div>
                    <div v-else>
                      <img-upload
                        :tips="imgTips"
                        @upload-data="getSettleCertFrontPhoto"
                      />
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="结算人身份证反面">
                    <div v-if="queryForm.settleCertBackPhoto != ''">
                      <ul class="upload-img-box">
                        <li class="upload-img">
                          <el-image
                            class="img"
                            :preview-src-list="[queryForm.settleCertBackPhoto]"
                            :src="queryForm.settleCertBackPhoto"
                            style="width: 100%; height: 100%"
                          />
                          <img
                            class="close"
                            src="~@/assets/index_images/close_icon.png"
                            @click="removeImgs(8)"
                          />
                        </li>
                      </ul>
                    </div>
                    <div v-else>
                      <img-upload
                        :tips="imgTips"
                        @upload-data="getSettleCertBackPhoto"
                      />
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="结算银行卡正面照">
                    <div v-if="queryForm.bankCardPhotoFront != ''">
                      <ul class="upload-img-box">
                        <li class="upload-img">
                          <el-image
                            class="img"
                            :preview-src-list="[queryForm.bankCardPhotoFront]"
                            :src="queryForm.bankCardPhotoFront"
                            style="width: 100%; height: 100%"
                          />
                          <img
                            class="close"
                            src="~@/assets/index_images/close_icon.png"
                            @click="removeImgs(9)"
                          />
                        </li>
                      </ul>
                    </div>
                    <div v-else>
                      <img-upload
                        :tips="imgTips"
                        @upload-data="getBankCardPhotoFront"
                      />
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="结算银行卡背面照">
                    <div v-if="queryForm.bankCardPhotoBack != ''">
                      <ul class="upload-img-box">
                        <li class="upload-img">
                          <el-image
                            class="img"
                            :preview-src-list="[queryForm.bankCardPhotoBack]"
                            :src="queryForm.bankCardPhotoBack"
                            style="width: 100%; height: 100%"
                          />
                          <img
                            class="close"
                            src="~@/assets/index_images/close_icon.png"
                            @click="removeImgs(10)"
                          />
                        </li>
                      </ul>
                    </div>
                    <div v-else>
                      <img-upload
                        :tips="imgTips"
                        @upload-data="getBankCardPhotoBack"
                      />
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="银行预留手机号">
                    <el-input
                      v-model="queryForm.openBankReservePhone"
                      placeholder="请输入银行预留手机号"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="附言">
                    <el-input
                      v-model="queryForm.postscript"
                      placeholder="请输入附言"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="付款用途和事由">
                    <el-input
                      v-model="queryForm.purpose"
                      placeholder="请输入付款用途和事由"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="开户意愿书照片">
                    <div v-if="queryForm.accountIntentPhoto != ''">
                      <ul class="upload-img-box">
                        <li class="upload-img">
                          <el-image
                            class="img"
                            :preview-src-list="[queryForm.accountIntentPhoto]"
                            :src="queryForm.accountIntentPhoto"
                            style="width: 100%; height: 100%"
                          />
                          <img
                            class="close"
                            src="~@/assets/index_images/close_icon.png"
                            @click="removeImgs(11)"
                          />
                        </li>
                      </ul>
                    </div>
                    <div v-else>
                      <img-upload
                        :tips="imgTips"
                        @upload-data="getAccountIntentPhoto"
                      />
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="代付证明">
                    <div v-if="queryForm.withdrawProofPhoto != ''">
                      <ul class="upload-img-box">
                        <li class="upload-img">
                          <el-image
                            class="img"
                            :preview-src-list="[queryForm.withdrawProofPhoto]"
                            :src="queryForm.withdrawProofPhoto"
                            style="width: 100%; height: 100%"
                          />
                          <img
                            class="close"
                            src="~@/assets/index_images/close_icon.png"
                            @click="removeImgs(12)"
                          />
                        </li>
                      </ul>
                    </div>
                    <div v-else>
                      <img-upload
                        :tips="imgTips"
                        @upload-data="getWithdrawProofPhoto"
                      />
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="转账功能申请书">
                    <div v-if="queryForm.transferApplyAttachment != ''">
                      <ul class="upload-img-box">
                        <li class="upload-img">
                          <el-image
                            class="img"
                            :preview-src-list="[queryForm.transferApplyAttachment]"
                            :src="queryForm.transferApplyAttachment"
                            style="width: 100%; height: 100%"
                          />
                          <img
                            class="close"
                            src="~@/assets/index_images/close_icon.png"
                            @click="removeImgs(13)"
                          />
                        </li>
                      </ul>
                    </div>
                    <div v-else>
                      <img-upload
                        :tips="imgTips"
                        @upload-data="getTransferApplyAttachment"
                      />
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="代付证明图片">
                    <div v-if="queryForm.settleAttachment != ''">
                      <ul class="upload-img-box">
                        <li class="upload-img">
                          <el-image
                            class="img"
                            :preview-src-list="[queryForm.settleAttachment]"
                            :src="queryForm.settleAttachment"
                            style="width: 100%; height: 100%"
                          />
                          <img
                            class="close"
                            src="~@/assets/index_images/close_icon.png"
                            @click="removeImgs(14)"
                          />
                        </li>
                      </ul>
                    </div>
                    <div v-else>
                      <img-upload
                        :tips="imgTips"
                        @upload-data="getSettleAttachment"
                      />
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="收款方营业执照图片">
                    <div v-if="queryForm.settleLicensePhoto != ''">
                      <ul class="upload-img-box">
                        <li class="upload-img">
                          <el-image
                            class="img"
                            :preview-src-list="[queryForm.settleLicensePhoto]"
                            :src="queryForm.settleLicensePhoto"
                            style="width: 100%; height: 100%"
                          />
                          <img
                            class="close"
                            src="~@/assets/index_images/close_icon.png"
                            @click="removeImgs(15)"
                          />
                        </li>
                      </ul>
                    </div>
                    <div v-else>
                      <img-upload
                        :tips="imgTips"
                        @upload-data="getSettleLicensePhoto"
                      />
                    </div>
                  </el-form-item>
                </el-col>
              </div>
              <!-- 受益人信息 -->
              <div v-if="active === 5">
                <el-col :span="24">
                  <el-form-item
                    label="实际控制人（受益人）姓名"
                    label-width="300px"
                    prop="controllerName"
                  >
                    <el-input
                      v-model="queryForm.controllerName"
                      placeholder="请输入实际控制人（受益人）姓名"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="实际控制人（受益人）证件类型"
                    label-width="300px"
                    prop="controllerType"
                    required
                  >
                    <el-select
                      v-model="queryForm.controllerType"
                      placeholder="请选择实际控制人（受益人）证件类型"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in identityList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="实际控制人（受益人）证件号码"
                    label-width="300px"
                    prop="controllerNo"
                  >
                    <el-input
                      v-model="queryForm.controllerNo"
                      placeholder="请输入实际控制人（受益人）证件号码"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="实际控制人（受益人）证件有效期（起始）"
                    label-width="300px"
                    prop="controllerFrom"
                    required
                  >
                    <el-date-picker
                      v-model="queryForm.controllerFrom"
                      placeholder="请选择实际控制人（受益人）证件起始有效期"
                      style="width: 700px"
                      type="date"
                      value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="实际控制人（受益人）证件有效期（截止）"
                    label-width="300px"
                    prop="controllerTo"
                    required
                  >
                    <el-date-picker
                      v-if="!controllerToLongTerm"
                      v-model="queryForm.controllerTo"
                      placeholder="请选择实际控制人（受益人）证件截止有效期"
                      style="width: 700px; margin-right: 40px"
                      type="date"
                      value-format="yyyy-MM-dd"
                    />
                    <el-checkbox
                      v-model="controllerToLongTerm"
                      @change="controllerToLongTermChange"
                    >
                      长期
                    </el-checkbox>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="实际控制人（受益人）证件地址（国籍）"
                    label-width="300px"
                    prop="controllerCountry"
                  >
                  <el-select
                      v-model="queryForm.controllerCountry"
                      filterable
                      placeholder="请选择实际控制人（受益人）证件地址（国籍）"
                      remote
                      :remote-method="getCountryList"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in nationList"
                        :key="item.id"
                        :label="item.countryName"
                        :value="item.countryCode"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="实际控制人（受益人）详细地址"
                    label-width="300px"
                    prop="controllerAddress"
                  >
                    <el-input
                      v-model="queryForm.controllerAddress"
                      placeholder="请输入实际控制人（受益人）详细地址"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="实际控制人（受益人）证件正面照"
                    label-width="300px"
                    required
                  >
                    <div v-if="queryForm.controllerPhotoFront != ''">
                      <ul class="upload-img-box">
                        <li class="upload-img">
                          <el-image
                            class="img"
                            :preview-src-list="[queryForm.controllerPhotoFront]"
                            :src="queryForm.controllerPhotoFront"
                            style="width: 100%; height: 100%"
                          />
                          <img
                            class="close"
                            src="~@/assets/index_images/close_icon.png"
                            @click="removeImgs(16)"
                          />
                        </li>
                      </ul>
                    </div>
                    <div v-else>
                      <img-upload @upload-data="getBeneficiaryIdFrontPhoto" />
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="实际控制人（受益人）证件背面照"
                    label-width="300px"
                    prop="controllerPhotoBack"
                    required
                  >
                    <div v-if="queryForm.controllerPhotoBack != ''">
                      <ul class="upload-img-box">
                        <li class="upload-img">
                          <el-image
                            class="img"
                            :preview-src-list="[queryForm.controllerPhotoBack]"
                            :src="queryForm.controllerPhotoBack"
                            style="width: 100%; height: 100%"
                          />
                          <img
                            class="close"
                            src="~@/assets/index_images/close_icon.png"
                            @click="removeImgs(17)"
                          />
                        </li>
                      </ul>
                    </div>
                    <div v-else>
                      <img-upload @upload-data="getBeneficiaryIdBackPhoto" />
                    </div>
                  </el-form-item>
                </el-col>
              </div>
            </div>
            <!-- 个人用户 -->
            <div v-else>
              <!-- 经营信息 -->
              <div v-if="active === 1">
                <el-col :span="24">
                  <el-form-item label="邮箱地址" prop="email">
                    <el-input
                      v-model="queryForm.email"
                      placeholder="请输入邮箱地址"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="省市区" required>
                    <el-cascader
                      v-model="areaValue"
                      :options="areaData"
                      placeholder="请选择省市区"
                      :props="{
                        value: 'areaCode',
                        label: 'areaName',
                        children: 'children',
                      }"
                      style="width: 100%"
                      @change="onChangeArea"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item :label="isEnterprise? '商户类别' : '商户类别(个人类别)'" prop="mcc">
                    <el-select
                      v-model="queryForm.mcc"
                      filterable
                      placeholder="请输入商户类别，例如：台球、洗浴、KTV、咖啡馆等"
                      remote
                      :remote-method="getMccList"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="(item, index) in mccList"
                        :key="index"
                        :label="item.name"
                        :value="item.code"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="经营详细地址" prop="businessAddress">
                    <el-input
                      v-model="queryForm.businessAddress"
                      placeholder="请输入经营详细地址"
                    />
                  </el-form-item>
                </el-col>
              </div>
              <!-- 个人身份信息 -->
              <div v-if="active === 2">
                <el-col :span="24">
                  <el-form-item label="证件类型" prop="lawyerCertType">
                    <el-select
                      v-model="queryForm.lawyerCertType"
                      placeholder="请选择个人证件类型"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in identityList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="证件号码" prop="lawyerCertNo">
                    <el-input
                      v-model="queryForm.lawyerCertNo"
                      placeholder="请输入所选择的证件号码"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="证件正面照" required>
                    <div v-if="queryForm.lawyerCertPhotoFront != ''">
                      <ul class="upload-img-box">
                        <li class="upload-img">
                          <el-image
                            class="img"
                            :preview-src-list="[queryForm.lawyerCertPhotoFront]"
                            :src="queryForm.lawyerCertPhotoFront"
                            style="width: 100%; height: 100%"
                          />
                          <img
                            class="close"
                            src="~@/assets/index_images/close_icon.png"
                            @click="removeImgs(4)"
                          />
                        </li>
                      </ul>
                    </div>
                    <div v-else>
                      <img-upload
                        :tips="imgTips"
                        @upload-data="getLegalIdFrontPhoto"
                      />
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="证件背面照" required>
                    <div v-if="queryForm.lawyerCertPhotoBack != ''">
                      <ul class="upload-img-box">
                        <li class="upload-img">
                          <el-image
                            class="img"
                            :preview-src-list="[queryForm.lawyerCertPhotoBack]"
                            :src="queryForm.lawyerCertPhotoBack"
                            style="width: 100%; height: 100%"
                          />
                          <img
                            class="close"
                            src="~@/assets/index_images/close_icon.png"
                            @click="removeImgs(5)"
                          />
                        </li>
                      </ul>
                    </div>
                    <div v-else>
                      <img-upload
                        :tips="imgTips"
                        @upload-data="getLegalIdBackPhoto"
                      />
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="证件人姓名" prop="certificateName">
                    <el-input
                      v-model="queryForm.certificateName"
                      placeholder="请输入证件人姓名"
                      @input="inCertificateName"
                    />
                  </el-form-item>
                </el-col>
                <el-col v-if="active === 2" :span="24">
                  <el-form-item label="证件有效期（起始）" prop="certificateFrom">
                    <el-date-picker
                      v-model="queryForm.certificateFrom"
                      placeholder="请选择证件起始有效期"
                      type="date"
                      value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="证件有效期（截止）" prop="certificateTo">
                    <el-date-picker
                      v-if="!certificateToLongTerm"
                      v-model="queryForm.certificateTo"
                      placeholder="请选择证件截止有效期"
                      style="margin-right: 40px"
                      type="date"
                      value-format="yyyy-MM-dd"
                    />
                    <el-checkbox
                      v-model="certificateToLongTerm"
                      @change="certificateToLongTermChange"
                    >
                      长期
                    </el-checkbox>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="手机号码" prop="legalPersonPhone">
                    <el-input
                      v-model="queryForm.legalPersonPhone"
                      placeholder="请输入手机号码"
                      type="number"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="证件地址（国籍）" prop="lawyerCountry">
                    <el-select
                      v-model="queryForm.lawyerCountry"
                      filterable
                      placeholder="请选择证件地址（国籍）"
                      remote
                      :remote-method="getCountryList"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in nationList"
                        :key="item.id"
                        :label="item.countryName"
                        :value="item.countryCode"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="证件详情地址" prop="lawyerAddress">
                    <el-input
                      v-model="queryForm.lawyerAddress"
                      placeholder="请输入证件详情地址"
                    />
                  </el-form-item>
                </el-col>
              </div>
              <!-- 注册账户信息 -->
              <div v-if="active === 3">
                <el-col :span="24">
                  <el-form-item label="账户类型" prop="openAccountType" required>
                    <el-radio-group v-model="queryForm.openAccountType" disabled>
                      <el-radio :label="'1'">对公账户</el-radio>
                      <el-radio :label="'2'">个人账户</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="账户名称" prop="licenceAccount">
                    <el-input
                      v-model="queryForm.licenceAccount"
                      placeholder="请输入账户名称"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="账户账号" prop="licenceAccountNo">
                    <el-input
                      v-model="queryForm.licenceAccountNo"
                      placeholder="请输入账户账号"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="开户银行" prop="licenceOpenBank">
                    <el-select
                      v-model="queryForm.licenceOpenBank"
                      filterable
                      placeholder="请输入开户银行"
                      remote
                      :remote-method="querySearch"
                      style="width: 100%"
                      @change="bankBranchSearch"
                    >
                      <el-option
                        v-for="item in bankData"
                        :key="item.id"
                        :label="item.bankName"
                        :value="item.bankName"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="证明文件(开户许可证)" required>
                    <div v-if="queryForm.openingLicenseAccountPhoto != ''">
                      <ul class="upload-img-box">
                        <li class="upload-img">
                          <el-image
                            class="img"
                            :preview-src-list="[
                              queryForm.openingLicenseAccountPhoto,
                            ]"
                            :src="queryForm.openingLicenseAccountPhoto"
                            style="width: 100%; height: 100%"
                          />
                          <img
                            class="close"
                            src="~@/assets/index_images/close_icon.png"
                            @click="removeImgs(6)"
                          />
                        </li>
                      </ul>
                    </div>
                    <div v-else>
                      <img-upload :tips="imgTips" @upload-data="getOpeningPermit" />
                    </div>
                  </el-form-item>
                </el-col>
              </div>
              <!-- 结算账户信息 -->
              <div v-if="active === 4">
                <el-col :span="24">
                  <el-form-item label="结算方式" prop="settleTarget">
                    <el-radio-group v-model="queryForm.settleTarget" disabled>
                      <el-radio :label="'1'">自动提现</el-radio>
                      <el-radio :label="'2'">手动提现</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="结算账户类型" prop="settleAccountType">
                    <el-radio-group v-model="queryForm.settleAccountType">
                      <el-radio disabled :label="'1'">对公账户</el-radio>
                      <el-radio disabled :label="'2'">个人账户</el-radio>
                      <el-radio disabled :label="'3'">授权对公</el-radio>
                      <el-radio disabled :label="'4'">授权对私</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="结算账户账号" prop="settleAccountNo">
                    <el-input
                      v-model="queryForm.settleAccountNo"
                      placeholder="请输入结算账户账号"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="结算账户名称" prop="settleAccount">
                    <el-input
                      v-model="queryForm.settleAccount"
                      placeholder="请输入结算账户名称"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="结算开户银行" prop="openBank">
                    <el-select
                      v-model="queryForm.openBank"
                      filterable
                      placeholder="请输入开户银行"
                      remote
                      :remote-method="querySearch"
                      style="width: 100%"
                      @change="bankBranchSearch"
                    >
                      <el-option
                        v-for="item in bankData"
                        :key="item.id"
                        :label="item.bankName"
                        :value="item.bankName"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </div>
              <!-- 受益人信息 -->
              <div v-if="active === 5">
                <el-col :span="24">
                  <el-form-item
                    label="实际控制人（受益人）姓名"
                    label-width="300px"
                    prop="controllerName"
                  >
                    <el-input
                      v-model="queryForm.controllerName"
                      placeholder="请输入实际控制人（受益人）姓名"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="实际控制人（受益人）证件类型"
                    label-width="300px"
                    prop="controllerType"
                    required
                  >
                    <el-select
                      v-model="queryForm.controllerType"
                      placeholder="请选择实际控制人（受益人）证件类型"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in identityList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="实际控制人（受益人）证件号码"
                    label-width="300px"
                    prop="controllerNo"
                  >
                    <el-input
                      v-model="queryForm.controllerNo"
                      placeholder="请输入实际控制人（受益人）证件号码"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="实际控制人（受益人）证件有效期（起始）"
                    label-width="300px"
                    prop="controllerFrom"
                    required
                  >
                    <el-date-picker
                      v-model="queryForm.controllerFrom"
                      placeholder="请选择实际控制人（受益人）证件起始有效期"
                      style="width: 700px"
                      type="date"
                      value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="实际控制人（受益人）证件有效期（截止）"
                    label-width="300px"
                    prop="controllerTo"
                    required
                  >
                    <el-date-picker
                      v-if="!controllerToLongTerm"
                      v-model="queryForm.controllerTo"
                      placeholder="请选择实际控制人（受益人）证件截止有效期"
                      style="width: 700px; margin-right: 40px"
                      type="date"
                      value-format="yyyy-MM-dd"
                    />
                    <el-checkbox
                      v-model="controllerToLongTerm"
                      @change="controllerToLongTermChange"
                    >
                      长期
                    </el-checkbox>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="实际控制人（受益人）证件地址（国籍）"
                    label-width="300px"
                    prop="controllerCountry"
                  >
                  <el-select
                      v-model="queryForm.controllerCountry"
                      filterable
                      placeholder="请选择实际控制人（受益人）证件地址（国籍）"
                      remote
                      :remote-method="getCountryList"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in nationList"
                        :key="item.id"
                        :label="item.countryName"
                        :value="item.countryCode"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="实际控制人（受益人）详细地址"
                    label-width="300px"
                    prop="controllerAddress"
                  >
                    <el-input
                      v-model="queryForm.controllerAddress"
                      placeholder="请输入实际控制人（受益人）详细地址"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="实际控制人（受益人）证件正面照"
                    label-width="300px"
                    required
                  >
                    <div v-if="queryForm.controllerPhotoFront != ''">
                      <ul class="upload-img-box">
                        <li class="upload-img">
                          <el-image
                            class="img"
                            :preview-src-list="[queryForm.controllerPhotoFront]"
                            :src="queryForm.controllerPhotoFront"
                            style="width: 100%; height: 100%"
                          />
                          <img
                            class="close"
                            src="~@/assets/index_images/close_icon.png"
                            @click="removeImgs(16)"
                          />
                        </li>
                      </ul>
                    </div>
                    <div v-else>
                      <img-upload @upload-data="getBeneficiaryIdFrontPhoto" />
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="实际控制人（受益人）证件背面照"
                    label-width="300px"
                    prop="controllerPhotoBack"
                    required
                  >
                    <div v-if="queryForm.controllerPhotoBack != ''">
                      <ul class="upload-img-box">
                        <li class="upload-img">
                          <el-image
                            class="img"
                            :preview-src-list="[queryForm.controllerPhotoBack]"
                            :src="queryForm.controllerPhotoBack"
                            style="width: 100%; height: 100%"
                          />
                          <img
                            class="close"
                            src="~@/assets/index_images/close_icon.png"
                            @click="removeImgs(17)"
                          />
                        </li>
                      </ul>
                    </div>
                    <div v-else>
                      <img-upload @upload-data="getBeneficiaryIdBackPhoto" />
                    </div>
                  </el-form-item>
                </el-col>
              </div>
            </div>
            <el-col :span="24">
              <div class="btn-box">
                <el-button
                  :disabled="active == 0"
                  icon="el-icon-arrow-left"
                  type="primary"
                  @click="onChangeStep(1)"
                >
                  上一步
                </el-button>
                <div v-if="isEnterprise">
                  <el-button
                    v-if="active < 5"
                    type="primary"
                    @click="onChangeStep(2)"
                  >
                    下一步
                    <i class="el-icon-arrow-right el-icon--right"></i>
                  </el-button>
                  <el-button v-else type="success" @click="addNewMerchant(2)">
                    确定注册
                    <i class="el-icon-s-check"></i>
                  </el-button>
                </div>
                <div v-else>
                  <el-button
                    v-if="active < 5"
                    type="primary"
                    @click="onChangeStep(2)"
                  >
                    下一步
                    <i class="el-icon-arrow-right el-icon--right"></i>
                  </el-button>
                  <el-button v-else type="success" @click="addNewMerchant(1)">
                    确定注册
                    <i class="el-icon-s-check"></i>
                  </el-button>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-drawer>
  </div>
</template>

<script>
  import { AddBusiness, mccSearchByName } from '@/api/business'
  import { areaList, queryByName, bankList, queryByBankName, bankBranchList } from '@/api/area'
  import ImgUpload from '@/components/uploading/index'
  export default {
    name: 'TableAdd',
    components: {
      ImgUpload,
    },
    data() {
      return {
        showDrawer: false, // 显示抽屉开关
        drawerTitle: '', // 抽屉标题
        active: 0, // 步骤
        isEnterprise: false, // 是否是企业
        isbusinessLicense: false, // 是否需要营业执照
        nationList: [], // 国家列表
        bankData: [], // 银行列表
        subBranchList: [], // 银行分行列表
        areaData: [], // 省市区
        areaValue: [], // 省市区值
        mccList: [], // 商户类别列表
        stepList: [
          // 步骤列表
          { id: '1', description: '基础信息', title: '第一步' },
        ],
        entityTypeList: [
          { id: '1', name: '个体工商户' },
          { id: '2', name: '企业' },
          { id: '3', name: '个人（小微）' },
          { id: '4', name: '政店事业单位' },
          { id: '9', name: '其他组织' },
        ],
        careerList: [
          {
            id: '1',
            name: '党的机关、国家机关、群众团体和社会组织、企事业单位负责人与业技术人员',
          },
          { id: '2', name: '与业技术人员' },
          { id: '3', name: '办事人员和有关人员' },
          { id: '4', name: '社会生产服务和生活服务人员' },
          { id: '5', name: '农、林、牧、渔业生产及辅劣人员' },
          { id: '6', name: '生产制造及有关人员' },
          { id: '7', name: '军人' },
          { id: '8', name: '学生' },
          { id: '9', name: '退休/无业' },
          { id: '10', name: '不便分类的其他从业人员' },
        ],
        identityList: [
          { id: '0', name: '身份证' },
          { id: '1', name: '居住证' },
          { id: '2', name: '签证' },
          { id: '3', name: '护照' },
          { id: '4', name: '户口本' },
          { id: '5', name: '军人证' },
          { id: '6', name: '团员证' },
          { id: '7', name: '党员证' },
          { id: '8', name: '港澳通行证' },
          { id: '9', name: '台胞证' },
          { id: '11', name: '临时身份证' },
          { id: '12', name: '回乡证' },
          { id: '13', name: '营业执照' },
          { id: '14', name: '组织机构代码证' },
          { id: '15', name: '驾驶证' },
          { id: '16', name: '外国人居留证' },
          { id: '99', name: '其他' },
        ],
        contactCertToLongTerm: false, // 证件截止有效期
        certificateToLongTerm: false, // 证件截止有效期
        businessLicenseToLongTerm: false, // 营业执照截止有效期
        controllerToLongTerm: false, // 实际控制人证件有效期(截止)
        queryForm: {
          merchantName: '', // 商户注册名称
          shortName: '', // 商户简称
          mcc: '', // 商户类别
          merchantType: '', // 主体类型：1-个体工商户；2-企业；3-个人(小微)；4-政府事业单位；9-其他组织
          soleInvestor: '1', // 是否个人独资企业，0否，1是
          acceptOrder: 1, // 是否收单：1是，0否 (仅商户)，暂时默认1
          contactPerson: '', // 联系人姓名
          contactPhone: '', // 联系人手机号码
          contactType: '1', // 身份类型：0经办人，1法人
          contactCertType: '', // 联系人证件类型，经办人时必需
          contactCertNo: '', // 联系人证件号码，经办人时必需
          contactPhotoFront: '', // 联系人证件照正面，经办人时必需
          contactPhotoBack: '', // 联系人证件照背面，经办人时必需
          contactCertFrom: '', // 联系人证件有效期(起始)，格式：yyyymmdd，经办人时必需
          contactCertTo: '', // 联系人证件有效期(截止)，格式：yyyymmdd，无限期填写"长期"，经办人时必需
          contactBusinessAuth: '', // 联系人业务办理授权函，经办人时必需
          certificateName: '', // 开户人姓名/证件人姓名 (共用)
          lawyerCertType: '', // 法人证件类型(共用)
          lawyerCertNo: '', // 法人证件号码(共用)
          lawyerCertPhotoFront: '', // 法人证件正面照 (共用)
          lawyerCertPhotoBack: '', // 法人证件背面照 (共用)
          lawyerName: '', // 法人姓名
          certificateFrom: '', // 法人证件效期(起始)，格式：yyyymmdd (共用)
          certificateTo: '', // 法人证件效期(截止)，格式：yyyymmdd，无限期填写"长期" (共用)
          legalPersonPhone: '', // 法人手机号码 (仅商户)
          businessLicenseType: '1', // 证照类型，默认为1营业执照
          businessLicenseCode: '', // 营业执照注册号
          businessLicenseName: '', // 营业执照名称
          businessLicensePhoto: '', // 营业执照照片
          businessScope: '', // 营业范围
          registerAddress: '', // 注册地址
          businessAddress: '', // 经营详细地址
          registeredCapital: '', // 注册资金
          businessLicenseFrom: '', // 营业执照有效期(起始)，格式：yyyymmdd (共用)
          businessLicenseTo: '', // 营业执照有效期(截止)，格式：yyyymmdd，无限期填写"长期" (共用)
          lawyerCountry: '', // 法人证件地址(国籍)，默认CHN(中国) (仅商户)
          lawyerAddress: '', // 法人证件详细地址，acceptOrder=1时必填 (仅商户)
          openAccountType: '', // 账户类型：1-对公账户 2-法人账户
          licenceAccount: '', // 账户名称
          licenceAccountNo: '', // 账户账号
          licenceOpenBank: '', // 开户银行
          licenceOpenSubBank: '', // 开户支行
          openingLicenseAccountPhoto: '', // 开户许可证照片
          settleTarget: '1', // 结算方式：1自动提现 2手动提现
          settleAccountType: '', // 结算类型：1:对公败户2:法人账户3:授权对公4:授权对私
          settleAccountNo: '', // 结算账户账号
          settleAccount: '', // 结算账户名称
          settleAccountAttr: '', // 账户属性
          openBank: '', // 结算开户银行
          openSubBank: '', // 结算开户支行
          openBankCode: '', // 开户联行号
          settleCertFrontPhoto: '', // 结算人身份证正面
          settleCertBackPhoto: '', // 结算人身份证反面
          bankCardPhotoFront: '', // 结算银行卡正面照
          bankCardPhotoBack: '', // 结算银行卡背面照
          openBankReservePhone: '', // 银行预留手机号
          postscript: '', // 附言
          purpose: '', // 付款用途和事由
          accountIntentPhoto: '', // 开户意愿书照片
          withdrawProofPhoto: '', // 代付证明
          transferApplyAttachment: '', // 转账功能申请书
          settleAttachment: '', // 结算账户附件
          settleLicensePhoto: '', // 收款方营业执照图片
          controllerName: '', // 实际控制人（受益人）姓名
          controllerType: '', // 实际控制人（受益人）证件类型
          controllerNo: '', // 实际控制人（受益人）证件号码
          controllerFrom: '', // 实际控制人（受益人）证件有效期(起始)
          controllerTo: '', // 实际控制人（受益人）证件有效期(截止)
          controllerCountry: '', // 实际控制人（受益人）证件地址（国籍）
          controllerAddress: '', // 实际控制人（受益人）详细地址
          controllerPhotoFront: '', // 实际控制人（受益人）证件正面照
          controllerPhotoBack: '', // 实际控制人（受益人）证件背面照
          sex: '0', // 性别 0未确定 1男 2女
          career: '', // 职业类型
          nationality: '', // 国籍
          personCountry: '', // 个人开户国籍
          remarks: '', // 备注
          email: '', // 邮箱
          province: '', // 省
          city: '', // 市
          district: '', // 区
          accountType: '3', // 开户许可证标识
          canDownType: '00', // 是否降级开户
          fileIdsJson: {}, // 存放文件上传的ids数据
        },
        rules: {
          merchantName: [
            { required: true, message: '请输入商户名称', trigger: 'blur' },
          ],
          shortName: [
            { required: true, message: '请输入商户简称', trigger: 'blur' },
          ],
          email: [
            { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          ],
          contactPerson: [
            { required: true, message: '请输入联系人姓名', trigger: 'blur' },
          ],
          contactPhone: [
            {
              required: true,
              message: '请输入联系人手机号码',
              trigger: 'blur',
            },
          ],
          contactType: [
            {
              required: true,
              message: '请选择身份类型',
              trigger: ['blur', 'change'],
            },
          ],
          career: [
            {
              required: true,
              message: '请选择职业类型',
              trigger: ['blur', 'change'],
            },
          ],
          contactCertNo: [
            {
              required: true,
              message: '请输入经办人证件号码',
              trigger: 'blur',
            },
          ],
          certificateName: [
            {
              required: true,
              message: '请输入证件人姓名',
              trigger: 'blur',
            },
          ],
          contactCertFrom: [
            {
              required: true,
              message: '请选择证件起始有效期',
              trigger: ['blur', 'change'],
            },
          ],
          contactCertTo: [
            {
              required: true,
              message: '请选择证件截止有效期',
              trigger: ['blur', 'change'],
            },
          ],
          nationality: [
            {
              required: true,
              message: '请选择国籍',
              trigger: ['blur', 'change'],
            },
          ],
          personCountry: [
            {
              required: true,
              message: '请选择国籍',
              trigger: ['blur', 'change'],
            },
          ],
          lawyerCertNo: [
            {
              required: true,
              message: '请输入所选择的证件号码',
              trigger: 'blur',
            },
          ],
          lawyerName: [
            {
              required: true,
              message: '请输入所选择的证件人姓名',
              trigger: 'blur',
            },
          ],
          certificateFrom: [
            {
              required: true,
              message: '请选择证件起始有效期',
              trigger: ['blur', 'change'],
            },
          ],
          certificateTo: [
            {
              required: true,
              message: '请选择证件截止有效期',
              trigger: ['blur', 'change'],
            },
          ],
          legalPersonPhone: [
            {
              required: true,
              message: '请输入所选择的证件人姓名',
              trigger: 'blur',
            },
          ],
          businessLicenseName: [
            {
              required: true,
              message: '请输入营业执照名称',
              trigger: 'blur',
            },
          ],
          businessLicenseCode: [
            {
              required: true,
              message: '请输入营业执照编号',
              trigger: 'blur',
            },
          ],
          registerAddress: [
            {
              required: true,
              message: '请输入营业执照注册地址',
              trigger: 'blur',
            },
          ],
          businessAddress: [
            {
              required: true,
              message: '请输入经营详细地址',
              trigger: 'blur',
            },
          ],
          businessScope: [
            {
              required: true,
              message: '请输入经营范围',
              trigger: 'blur',
            },
          ],
          registeredCapital: [
            {
              required: true,
              message: '请输入注册资本',
              trigger: 'blur',
            },
          ],
          businessLicenseFrom: [
            {
              required: true,
              message: '请选择证件起始有效期',
              trigger: ['blur', 'change'],
            },
          ],
          businessLicenseTo: [
            {
              required: true,
              message: '请选择证件截止有效期',
              trigger: ['blur', 'change'],
            },
          ],
          lawyerCountry: [
            {
              required: true,
              message: '请选择法人证件地址',
              trigger: ['blur', 'change'],
            },
          ],
          lawyerAddress: [
            {
              required: true,
              message: '请输入法人证件详情地址',
              trigger: 'blur',
            },
          ],
          licenceAccount: [
            {
              required: true,
              message: '请输入账户名称',
              trigger: 'blur',
            },
          ],
          licenceAccountNo: [
            {
              required: true,
              message: '请输入账户账号',
              trigger: 'blur',
            },
          ],
          settleAccount: [
            {
              required: true,
              message: '请输入账户账号',
              trigger: 'blur',
            },
          ],
          licenceOpenBank: [
            {
              required: true,
              message: '请选择开户银行',
              trigger: ['blur', 'change'],
            },
          ],
          settleAccountNo: [
            {
              required: true,
              message: '请输入结算账户账号',
              trigger: 'blur',
            },
          ],
          licenceOpenSubBank: [
            {
              required: true,
              message: '请选择开户银行支行',
              trigger: ['blur', 'change'],
            },
          ],
          openBank: [
            {
              required: true,
              message: '请选择结算开户银行',
              trigger: ['blur', 'change'],
            },
          ],
          mcc: [
            {
              required: true,
              message: '请输入商户类别，例如：台球、洗浴、KTV、咖啡馆等',
              trigger: ['blur', 'change'],
            },
          ],
          openSubBank: [
            {
              required: true,
              message: '请选择结算开户支行',
              trigger: ['blur', 'change'],
            },
          ],
          openBankCode: [
            {
              required: true,
              message: '请输入开户联行号',
              trigger: 'blur',
            },
          ],
          controllerName: [
            {
              required: true,
              message: '请输入实际控制人（受益人）姓名',
              trigger: 'blur',
            },
          ],
          controllerNo: [
            {
              required: true,
              message: '请输入实际控制人（受益人）证件号码',
              trigger: 'blur',
            },
          ],
          controllerFrom: [
            {
              required: true,
              message: '请选择实际控制人（受益人）证件起始有效期',
              trigger: 'change',
            },
            {
              required: true,
              message: '请选择实际控制人（受益人）证件起始有效期',
              trigger: 'blur',
            },
          ],
          controllerTo: [
            {
              required: true,
              message: '请选择实际控制人（受益人）证件截止有效期',
              trigger: 'change',
            },
            {
              required: true,
              message: '请选择实际控制人（受益人）证件截止有效期',
              trigger: 'blur',
            },
          ],
          controllerCountry: [
            {
              required: true,
              message: '请选择实际控制人（受益人）证件地址（国籍）',
              trigger: ['blur', 'change'],
            },
          ],
          controllerAddress: [
            {
              required: true,
              message: '请输入实际控制人（受益人）详细地址',
              trigger: 'blur',
            },
          ],
        },
        imgTips: '请上传小于5MB的图片',
        detailImgTips:
          '尺寸要求： 750像素*高度像素不限  注意图片的宽度必须是750像素',
      }
    },
    computed: {},
    beforeMount() {},
    // Removed empty beforeMount lifecycle hook
    beforeDestroy() {},
    /**
     * Vue 组件的生命周期钩子，在实例创建完成后立即调用。
     * 在此阶段可以进行数据的初始化等操作。
     */
    created() {
      // 获取省市区数据
      this.getAreaList()
    },
    methods: {
      // 法人姓名
      inLawyerName(e) {
        this.queryForm.certificateName = e
      },
      // 获取 mcc 列表
      async getMccList(e) {
        const { code, data } = await mccSearchByName({ name: e })
        if (code == '00000') {
          this.mccList = data
        }
      },
      // 个人证件人名称
      inCertificateName(e) {
        this.queryForm.lawyerName = e
      },
      // 证件有效期是否长期
      handleContactCertToLongTerm(val) {
        if (val) {
          this.queryForm.contactCertTo = '长期'
        } else {
          this.queryForm.contactCertTo = ''
        }
      },
      // 证件有效期是否长期
      certificateToLongTermChange(val) {
        if (val) {
          this.queryForm.certificateTo = '长期'
        } else {
          this.queryForm.certificateTo = ''
        }
      },
      // 营业执照有效期是否长期
      businessLicenseToLongTermChange(val) {
        if (val) {
          this.queryForm.businessLicenseTo = '长期'
        } else {
          this.queryForm.businessLicenseTo = ''
        }
      },
      // 证件有效期是否长期
      controllerToLongTermChange(val) {
        if (val) {
          this.queryForm.controllerTo = '长期'
        } else {
          this.queryForm.controllerTo = ''
        }
      },
      // 步骤事件
      onChangeStep(type) {
        if (type === 1) {
          if (this.active > 0) {
            this.active -= 1
          }
        } else if (type === 2) {
          if (this.active == 0) {
            if (this.ifValue('merchantType')) {
              this.$baseMessage(
                '请选择主体类型',
                'error',
                'vab-hey-message-error'
              )
              return false
            } else if (this.ifValue('merchantName')) {
              this.$baseMessage(
                '请输入商户名称',
                'error',
                'vab-hey-message-error'
              )
              return false
            } else if (this.ifValue('shortName')) {
              this.$baseMessage(
                '请输入商户简称',
                'error',
                'vab-hey-message-error'
              )
              return false
            } else if (this.ifValue('contactPerson')) {
              this.$baseMessage(
                '请输入联系人姓名',
                'error',
                'vab-hey-message-error'
              )
              return false
            } else if (this.ifValue('contactPhone')) {
              this.$baseMessage(
                '请输入手机号码',
                'error',
                'vab-hey-message-error'
              )
              return false
            }
          } else if (this.active == 1) {
            if (['1', '2', '4', '9'].includes(this.queryForm.merchantType)) {
              if (this.ifValue('email')) {
                this.$baseMessage(
                  '请输入邮箱地址',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('province')) {
                this.$baseMessage(
                  '请选择省市区',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('mcc')) {
                this.$baseMessage(
                  '请输入商户类别，例如：台球、洗浴、KTV、咖啡馆等',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('businessLicensePhoto')) {
                this.$baseMessage(
                  '请上传营业执照',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('businessLicenseName')) {
                this.$baseMessage(
                  '请输入营业执照名称',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('businessLicenseCode')) {
                this.$baseMessage(
                  '请输入营业执照编号',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('registerAddress')) {
                this.$baseMessage(
                  '请输入营业执照注册地址',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('businessAddress')) {
                this.$baseMessage(
                  '请输入经营详细地址',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('businessScope')) {
                this.$baseMessage(
                  '请输入营业执照经营范围',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('registeredCapital')) {
                this.$baseMessage(
                  '请输入注册资本',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('businessLicenseFrom')) {
                this.$baseMessage(
                  '请选择营业执照（起始）有效期',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('businessLicenseTo')) {
                this.$baseMessage(
                  '请选择营业执照（截止）有效期',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              }
            } else if (['3'].includes(this.queryForm.merchantType)) {
              if (this.ifValue('email')) {
                this.$baseMessage(
                  '请输入邮箱地址',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('province')) {
                this.$baseMessage(
                  '请选择省市区',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('mcc')) {
                this.$baseMessage(
                  '请输入商户类别(个人类别)，例如：台球、洗浴、KTV、咖啡馆等',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('businessAddress')) {
                this.$baseMessage(
                  '请输入经营详细地址',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              }
            }
          } else if (this.active == 2) {
            if (['1', '2', '4', '9'].includes(this.queryForm.merchantType)) {
              if (this.ifValue('lawyerCertType')) {
                this.$baseMessage(
                  '请选择法人证件类型',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('lawyerCertNo')) {
                this.$baseMessage(
                  '请输入法人证件号码',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('lawyerCertPhotoFront')) {
                this.$baseMessage(
                  '请上传法人证件正面照',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('lawyerCertPhotoBack')) {
                this.$baseMessage(
                  '请上传法人证件背面照',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('lawyerName')) {
                this.$baseMessage(
                  '请输入法人证件姓名',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('certificateName')) {
                this.$baseMessage(
                  '请输入法人证件姓名',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('certificateFrom')) {
                this.$baseMessage(
                  '请选择法人证件有效期（起始）',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('certificateTo')) {
                this.$baseMessage(
                  '请选择法人证件有效期（截止）',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('legalPersonPhone')) {
                this.$baseMessage(
                  '请输入法人手机号码',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('lawyerCountry')) {
                this.$baseMessage(
                  '请输入法人证件国籍',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('lawyerAddress')) {
                this.$baseMessage(
                  '请输入法人证件详情地址',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              }
            } else if (['3'].includes(this.queryForm.merchantType)) {
              if (this.ifValue('lawyerCertType')) {
                this.$baseMessage(
                  '请选个人证件类型',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('lawyerCertNo')) {
                this.$baseMessage(
                  '请输入个人证件号码',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('lawyerCertPhotoFront')) {
                this.$baseMessage(
                  '请上传个人证件正面照',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('lawyerCertPhotoBack')) {
                this.$baseMessage(
                  '请上传个人证件背面照',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('certificateName')) {
                    this.$baseMessage(
                    '请输入个人证件姓名',
                    'error',
                    'vab-hey-message-error'
                  )
                  return false
              } else if (this.ifValue('certificateFrom')) {
                this.$baseMessage(
                  '请选择个人证件有效期（起始）',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('certificateTo')) {
                this.$baseMessage(
                  '请选择个人证件有效期（截止）',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('legalPersonPhone')) {
                this.$baseMessage(
                  '请输入个人手机号码',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('lawyerCountry')) {
                this.$baseMessage(
                  '请输入个人证件国籍',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              } else if (this.ifValue('lawyerAddress')) {
                this.$baseMessage(
                  '请输入个人证件详情地址',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              }
            }
          } else if (this.active === 3) {
            if (this.ifValue('licenceAccount')) {
              this.$baseMessage(
                '请输入账户名称',
                'error',
                'vab-hey-message-error'
              )
              return false
            } else if (this.ifValue('licenceAccountNo')) {
              this.$baseMessage(
                '请输入账户账号',
                'error',
                'vab-hey-message-error'
              )
              return false
            } else if (this.ifValue('licenceOpenBank')) {
              this.$baseMessage(
                '请选择开户银行',
                'error',
                'vab-hey-message-error'
              )
              return false
            } else if (['1', '2', '4', '9'].includes(this.queryForm.merchantType)) {
              if (this.queryForm.openAccountType == '1' && this.ifValue('licenceOpenSubBank')) {
                this.$baseMessage(
                  '请选择开户支行',
                  'error',
                  'vab-hey-message-error'
                )
                return false
              }
            } else if (this.ifValue('openingLicenseAccountPhoto')) {
                this.$baseMessage(
                '请上传证明文件(开户许可证)',
                'error',
                'vab-hey-message-error'
              )
              return false
            }
          } else if (this.active === 4) {
            if (this.ifValue('settleAccountNo')) {
              this.$baseMessage(
                '请输入结算账户账号',
                'error',
                'vab-hey-message-error'
              )
              return false
            } else if (this.ifValue('settleAccount')) {
              this.$baseMessage(
                '请输入结算账户名称',
                'error',
                'vab-hey-message-error'
              )
              return false
            } else if (this.ifValue('openBank')) {
              this.$baseMessage(
                '请选择结算开户银行',
                'error',
                'vab-hey-message-error'
              )
              return false
            } else if (['1', '2', '4', '9'].includes(this.queryForm.merchantType)) {
              if (this.queryForm.settleAccountType == '1') {
                if (this.ifValue('openSubBank')) {
                  this.$baseMessage(
                    '请选择结算开户支行',
                    'error',
                    'vab-hey-message-error'
                  )
                  return false
                } else if (this.ifValue('openBankCode')) {
                  this.$baseMessage(
                    '请输入开户联行号',
                    'error',
                    'vab-hey-message-error'
                  )
                  return false
                }
              }
            }
          }
          if (this.active < 7) {
            this.active += 1
          }
        }
      },
      // 判断属性是否为空
      ifValue(e) {
        if (this.queryForm[e] === '' || this.queryForm[e] === null) {
          return true
        } else {
          return false
        }
      },
      // 确定新增商户
      async addNewMerchant() {
        if (this.ifValue('controllerName')) {
          this.$baseMessage(
            '请输入实际控制人（受益人）姓名',
            'error',
            'vab-hey-message-error'
          )
          return false
        } else if (this.ifValue('controllerType')) {
          this.$baseMessage(
            '请选择实际控制人（受益人）证件类型',
            'error',
            'vab-hey-message-error'
          )
          return false
        } else if (this.ifValue('controllerNo')) {
          this.$baseMessage(
            '请输入实际控制人（受益人）证件号码',
            'error',
            'vab-hey-message-error'
          )
          return false
        } else if (this.ifValue('controllerFrom')) {
          this.$baseMessage(
            '请选择实际控制人（受益人）证件有效期（起始）',
            'error',
            'vab-hey-message-error'
          )
          return false
        } else if (this.ifValue('controllerTo')) {
          this.$baseMessage(
            '请选择实际控制人（受益人）证件有效期（截止）',
            'error',
            'vab-hey-message-error'
          )
          return false
        } else if (this.ifValue('controllerAddress')) {
          this.$baseMessage(
            '请输入实际控制人（受益人）详细地址',
            'error',
            'vab-hey-message-error'
          )
          return false
        } else if (this.ifValue('controllerPhotoFront')) {
          this.$baseMessage(
            '请上传实际控制人（受益人）证件正面照',
            'error',
            'vab-hey-message-error'
          )
          return false
        } else if (this.ifValue('controllerPhotoBack')) {
          this.$baseMessage(
            '请上传实际控制人（受益人）证件背面照',
            'error',
            'vab-hey-message-error'
          )
          return false
        } else {
          let { code, msg } = await AddBusiness(this.queryForm)
          if (code == '00000') {
            this.$baseMessage(
              msg || '新增成功',
              'success',
              'vab-hey-message-success'
            )
            this.$emit('fetch-data')
            this.showDrawer = false
          }
        }
      },
      // 获取省市区数据
      async getAreaList() {
        try {
          // 尝试从缓存获取数据
          const cachedData = this.getAreaDataFromCache();

          // 如果有缓存数据，直接使用
          if (cachedData) {
            this.areaData = cachedData;
            return;
          }

          // 没有缓存或缓存已过期，请求新数据
          const { data } = await areaList({});

          // 缓存数据
          this.saveAreaDataToCache(data);

          // 更新视图
          this.areaData = data;
        } catch (error) {
          console.error('获取省市区数据失败:', error);
          // 确保在错误情况下至少有空数组
          this.areaData = [];
        }
      },

      /**
       * 从缓存获取省市区数据
       * @returns {Array|null} 缓存的省市区数据或null
       */
      getAreaDataFromCache() {
        try {
          const cacheKey = 'areaListData';
          const expireKey = 'areaListExpire';

          // 获取缓存数据和过期时间
          const cachedData = localStorage.getItem(cacheKey);
          const expireTime = localStorage.getItem(expireKey);

          // 验证缓存是否存在
          if (!cachedData || !expireTime) {
            return null;
          }

          // 检查是否过期（默认24小时）
          if (Date.now() > parseInt(expireTime)) {
            // 已过期，清除缓存
            localStorage.removeItem(cacheKey);
            localStorage.removeItem(expireKey);
            return null;
          }

          // 解析并返回缓存数据
          return JSON.parse(cachedData);
        } catch (error) {
          // 处理任何可能的错误（如JSON解析错误）
          console.error('读取省市区缓存失败:', error);
          // 清除可能损坏的缓存
          localStorage.removeItem('areaListData');
          localStorage.removeItem('areaListExpire');
          return null;
        }
      },

      /**
       * 将省市区数据保存到缓存中
       * @param {Array} data 要缓存的省市区数据
       */
      saveAreaDataToCache(data) {
        try {
          const cacheKey = 'areaListData';
          const expireKey = 'areaListExpire';
          const expireTime = Date.now() + (7 * 24 * 60 * 60 * 1000); // 7天过期

          // 保存数据和过期时间
          localStorage.setItem(cacheKey, JSON.stringify(data));
          localStorage.setItem(expireKey, expireTime.toString());
        } catch (error) {
          console.error('缓存省市区数据失败:', error);
          // 出错时尝试清除可能部分写入的缓存
          try {
            localStorage.removeItem('areaListData');
            localStorage.removeItem('areaListExpire');
          } catch (e) {
            console.error('清除损坏缓存失败:', e);
          }
        }
      },
      // 获取国家数据
      async getCountryList(e) {
        if (e) {
          const { code, data } = await queryByName({
            countryName: e,
          })
          if (code == '00000') {
            this.nationList = data
          }
        }
      },
      // 获取银行数据
      async getBankList() {
        const {
          code,
          data: { data },
        } = await bankList({
          pageSize: 999,
          currentPage: 1,
          operType: 0,
        })
        if (code == '00000') {
          this.bankData = data.map((item) => {
            // 创建一个新的对象，保留原有的属性，但将 bankName 替换为 value
            return {
              ...item,
              value: item.bankName,
            }
          })
        }
      },
      // 省市区事件
      onChangeArea(e) {
        this.queryForm.province = e[0]
        this.queryForm.city = e[1]
        this.queryForm.district = e[2]
      },
      // 选择主体类型事件
      changeEntityType(e) {
        if (['1', '2', '4', '9'].includes(e)) {
          this.isEnterprise = true
          this.stepList = [
            { id: 1, description: '基础信息', title: '第一步' },
            { id: 2, description: '经营信息', title: '第二步' },
            { id: 3, description: '法人身份信息', title: '第三步' },
            { id: 4, description: '注册账户信息', title: '第四步' },
            { id: 5, description: '结算账户信息', title: '第五步' },
            { id: 6, description: '受益人信息', title: '第六步' },
          ]
          this.queryForm.openAccountType = '1'
          this.queryForm.settleAccountType = '1'
        } else if (['3'].includes(e)) {
          this.isEnterprise = false // 是否是企业
          this.queryForm.soleInvestor = '0' // 默认传递0
          this.isbusinessLicense = false
          this.stepList = [
          { id: 1, description: '基础信息', title: '第一步' },
            { id: 2, description: '经营信息', title: '第二步' },
            { id: 3, description: '个人身份信息', title: '第三步' },
            { id: 4, description: '注册账户信息', title: '第四步' },
            { id: 5, description: '结算账户信息', title: '第五步' },
            { id: 6, description: '受益人信息', title: '第六步' },
          ]
          this.queryForm.openAccountType = '2'
          this.queryForm.settleAccountType = '2'
        }
      },
      // 返回开户银行输入建议的方法
      async querySearch(queryString) {
        const { code, data } = await queryByBankName({
          bankName: queryString,
        })
        if (code == '00000') {
          this.bankData = data
        }
      },
      // 返回开户支行输入建议的方法
      async bankBranchSearch(e) {
        let { code, data } = await bankBranchList({ bankName: e })
        if (code == '00000') {
          this.subBranchList = data
        }
      },
      // 选中结算银行事件
      async selectSettlementBankName(e) {
        let { code, data } = await bankBranchList({ bankName: e.bankName })
        if (code == '00000') {
          this.subBranchList = data.map((item) => {
            return {
              ...item,
              value: item.bankName,
            }
          })
        }
      },
      // 选中结算支行事件
      selectSettlementBankBranch(e) {
        console.log(e)
      },
      // 显示抽屉
      show(row) {
        if (row) {
          this.showDrawer = true // 抽屉开关
          this.drawerTitle = '编辑商户' // 抽屉标题
          this.active = 1 // 步骤
        } else {
          this.showDrawer = true // 抽屉开关
          this.drawerTitle = '新增商户' // 抽屉标题
        }
      },
      // 关闭抽屉
      close() {
        this.showDrawer = false
      },
      // 用于关闭 Drawer, 该方法会调用传入的 before-close 方法
      handleClose(done) {
        done()
      },
      // 得到证件正面照
      getIdFrontPhoto(data) {
        this.queryForm.contactPhotoFront = data.ossUrl
        this.queryForm.fileIdsJson['contactPhotoFront'] = data.fileId
      },
      // 得到证件背面照
      getIdBackPhoto(data) {
        this.queryForm.contactPhotoBack = data.ossUrl
        this.queryForm.fileIdsJson['contactPhotoBack'] = data.fileId
      },
      // 得到业务办理授权函
      getBusinessAuthFile(data) {
        this.queryForm.contactBusinessAuth = data.ossUrl
        this.queryForm.fileIdsJson['contactBusinessAuth'] = data.fileId
      },
      // 得到法人证件正面照
      getLegalIdFrontPhoto(data) {
        this.queryForm.lawyerCertPhotoFront = data.ossUrl
        this.queryForm.fileIdsJson['lawyerCertPhotoFront'] = data.fileId
      },
      // 得到法人证件背面照
      getLegalIdBackPhoto(data) {
        this.queryForm.lawyerCertPhotoBack = data.ossUrl
        this.queryForm.fileIdsJson['lawyerCertPhotoBack'] = data.fileId
      },
      // 得到营业执照图片
      getBusinessLicensePhoto(data) {
        this.queryForm.businessLicensePhoto = data.ossUrl
        this.queryForm.fileIdsJson['businessLicensePhoto'] = data.fileId
      },
      // 得到（开户许可证）图片
      getOpeningPermit(data) {
        this.queryForm.openingLicenseAccountPhoto = data.ossUrl
        this.queryForm.fileIdsJson['openingLicenseAccountPhoto'] = data.fileId
      },
      // 得到结算人身份证正面图片
      getSettleCertFrontPhoto(data) {
        this.queryForm.settleCertFrontPhoto = data.ossUrl
        this.queryForm.fileIdsJson['settleCertFrontPhoto'] = data.fileId
      },
      // 得到结算人身份证反面图片
      getSettleCertBackPhoto(data) {
        this.queryForm.settleCertBackPhoto = data.ossUrl
        this.queryForm.fileIdsJson['settleCertBackPhoto'] = data.fileId
      },
      // 得到结算银行卡正面照图片
      getBankCardPhotoFront(data) {
        this.queryForm.bankCardPhotoFront = data.ossUrl
        this.queryForm.fileIdsJson['bankCardPhotoFront'] = data.fileId
      },
      // 得到结算银行卡正面照图片
      getBankCardPhotoBack(data) {
        this.queryForm.bankCardPhotoBack = data.ossUrl
        this.queryForm.fileIdsJson['bankCardPhotoBack'] = data.fileId
      },
      // 得到开户意愿书图片
      getAccountIntentPhoto(data) {
        this.queryForm.accountIntentPhoto = data.ossUrl
        this.queryForm.fileIdsJson['accountIntentPhoto'] = data.fileId
      },
      // 得到代付证明图片
      getWithdrawProofPhoto(data) {
        this.queryForm.withdrawProofPhoto = data.ossUrl
        this.queryForm.fileIdsJson['withdrawProofPhoto'] = data.fileId
      },
      // 得到转账功能申请书图片
      getTransferApplyAttachment(data) {
        this.queryForm.transferApplyAttachment = data.ossUrl
        this.queryForm.fileIdsJson['transferApplyAttachment'] = data.fileId
      },
      // 得到结算账户附件图片
      getSettleAttachment(data) {
        this.queryForm.settleAttachment = data.ossUrl
        this.queryForm.fileIdsJson['settleAttachment'] = data.fileId
      },
      // 得到收款方营业执照图片
      getSettleLicensePhoto(data) {
        this.queryForm.settleLicensePhoto = data.ossUrl
        this.queryForm.fileIdsJson['settleLicensePhoto'] = data.fileId
      },
      // 得到实际控制人（受益人）证件正面照
      getBeneficiaryIdFrontPhoto(data) {
        this.queryForm.controllerPhotoFront = data.ossUrl
        this.queryForm.fileIdsJson['controllerPhotoFront'] = data.fileId
      },
      // 得到实际控制人（受益人）证件背面照
      getBeneficiaryIdBackPhoto(data) {
        this.queryForm.controllerPhotoBack = data.ossUrl
        this.queryForm.fileIdsJson['controllerPhotoBack'] = data.fileId
      },
      // 删除图片
      removeImgs(type) {
        this.$baseConfirm('你确定要删除当前项吗', null, () => {
          if (type === 1) {
            this.queryForm.contactPhotoFront = ''
          } else if (type === 2) {
            this.queryForm.contactPhotoBack = ''
          } else if (type === 3) {
            this.queryForm.contactBusinessAuth = ''
          } else if (type === 4) {
            this.queryForm.lawyerCertPhotoFront = ''
          } else if (type === 5) {
            this.queryForm.lawyerCertPhotoBack = ''
          } else if (type === 6) {
            this.queryForm.openingLicenseAccountPhoto = ''
          } else if (type === 7) {
            this.queryForm.settleCertFrontPhoto = ''
          } else if (type === 8) {
            this.queryForm.settleCertBackPhoto = ''
          } else if (type === 9) {
            this.queryForm.bankCardPhotoFront = ''
          } else if (type === 10) {
            this.queryForm.bankCardPhotoBack = ''
          } else if (type === 11) {
            this.queryForm.accountIntentPhoto = ''
          } else if (type === 12) {
            this.queryForm.withdrawProofPhoto = ''
          } else if (type === 13) {
            this.queryForm.transferApplyAttachment = ''
          } else if (type === 14) {
            this.queryForm.settleAttachment = ''
          } else if (type === 15) {
            this.queryForm.settleLicensePhoto = ''
          } else if (type === 16) {
            this.queryForm.controllerPhotoFront = ''
          } else if (type === 17) {
            this.queryForm.controllerPhotoBack = ''
          } else if (type === 18) {
            this.queryForm.businessLicensePhoto = ''
          }
          this.$baseMessage('删除成功', 'success', 'vab-hey-message-success')
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .btn-box {
    width: 600px;
    display: flex;
    margin: 60px auto 100px;
    justify-content: space-around;
  }
  .info-desc {
    font-size: 12px;
    color: #999;
  }
  .deleteInfo {
    margin-left: 10px;
  }
  .pay-button-group {
    display: block;
    margin: 20px auto;
    text-align: center;
  }
  .upload-img-box {
    padding: 0;
    margin: 0;
  }
  .upload-img {
    position: relative;
    box-sizing: border-box;
    display: inline-block;
    width: 148px;
    height: 148px;
    margin: 0 12px 12px 0;
    background-color: #fff;
    border: 1px solid #c0ccda;
    border-radius: 6px;
  }
  .upload-img .img {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
  .upload-img .close {
    position: absolute;
    top: -12px;
    right: -12px;
    width: 24px;
    height: 24px;
    cursor: pointer;
  }
  .textTips {
    padding: 0px 0px;
    font-size: 12px;
    color: #f56c6c;
  }
  .specs-content {
    display: flex;
    flex-wrap: wrap;
    .specs-li {
      width: 700px;
      margin-right: 10px;
      .header-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-left {
          display: flex;
          align-items: center;
        }
      }
      .value-content {
        display: flex;
        flex-direction: column;
        .value-li {
          display: flex;
          align-items: center;
          justify-content: space-around;
          margin: 5px 0px;
          .delete-value {
            margin-left: 10px;
          }
        }
        .add-specs-value {
          margin-top: 10px;
        }
      }
    }
  }
</style>
<style>

.el-radio input[aria-hidden="true"] {
  display: none !important;
}

.el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
  box-shadow: none !important;
}

</style>
