<template>
  <div class="goods-add-container">
    <el-page-header content="新增餐品" @back="goBack" />
    <div style="width: 600px; margin: 30px auto">
      <el-steps :active="active" align-center finish-status="success">
        <el-step description="填写餐品信息" title="第一步" />
        <el-step description="操作结果" title="第二步" />
      </el-steps>
    </div>
    <!-- 第一步 -->
    <div v-if="active == 1">
      <el-form
        class="demo-form"
        label-position="right"
        label-width="160px"
        :model="queryForm"
      >
        <el-row :gutter="0">
          <el-col :span="24">
            <el-form-item label="餐品类型" required>
              <el-select
                v-model="queryForm.categoryId"
                filterable
                placeholder="请选择餐品类型"
                style="width: 800px"
              >
                <el-option
                  v-for="item in FoodTypeData"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="餐品名称" required>
              <el-input
                v-model="queryForm.name"
                :clearable="true"
                placeholder="请输入餐品名称"
                style="width: 800px"
                type="text"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="餐品描述">
              <el-input
                v-model="queryForm.description"
                :clearable="true"
                maxlength="8000"
                placeholder="请输入餐品描述"
                show-word-limit
                style="width: 800px"
                type="textarea"
              />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="24">
            <el-form-item label="价格" required>
              <el-input
                v-model="queryForm.price"
                :clearable="true"
                placeholder="请输入餐品名称"
                style="width: 800px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="库存" required>
              <el-input
                v-model="queryForm.stock"
                :clearable="true"
                placeholder="请输入库存"
                style="width: 800px"
                type="number"
              />
            </el-form-item>
          </el-col> -->
          <!-- <el-col :span="24">
            <el-form-item label="规格" required>
              <div v-if="specs.length > 0" class="specs-content">
                <div
                  v-for="(item, index) in specs"
                  :key="index"
                  class="specs-li"
                >
                  <el-card>
                    <div slot="header" class="header-content">
                      <div class="header-left">
                        <span>规格名称：</span>
                        <el-input
                          v-model="item.title"
                          clearable
                          placeholder="请输入规格名称"
                          style="width: 200px"
                        />
                      </div>
                      <div class="header-right"></div>
                    </div>
                    <div class="value-content">
                      <div
                        v-for="(v, i) in item.value"
                        :key="i"
                        class="value-li"
                      >
                        <span class="label">口味：</span>
                        <el-input
                          v-model="v.name"
                          clearable
                          placeholder="请输入口味"
                          type="text"
                        />
                        <span class="label">库存：</span>
                        <el-input
                          v-model="v.inventory"
                          clearable
                          placeholder="请输入库存"
                          type="number"
                        />
                        <el-button
                          circle
                          class="delete-value"
                          icon="el-icon-delete"
                          size="mini"
                          type="danger"
                          @click="deleteValue(index, i)"
                        />
                      </div>
                      <el-button
                        class="add-specs-value"
                        icon="el-icon-plus"
                        plain
                        @click="addSpecsValue(index)"
                      >
                        添加规格
                      </el-button>
                    </div>
                  </el-card>
                </div>
              </div>
            </el-form-item>
          </el-col> -->
          <el-col :span="24">
            <el-form-item label="餐品图片" required>
              <div v-if="queryForm.imageUrl != ''">
                <ul class="upload-img-box">
                  <li class="upload-img">
                    <el-image
                      class="img"
                      :preview-src-list="[queryForm.imageUrl]"
                      :src="queryForm.imageUrl"
                      style="width: 100%; height: 100%"
                    />
                    <img
                      class="close"
                      src="~@/assets/index_images/close_icon.png"
                      @click="removeImgs(1)"
                    />
                  </li>
                </ul>
              </div>
              <div v-else>
                <img-upload :tips="''" @upload-data="getImageUrl" />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="餐品详情" required>
              <div style="max-width: 100%; min-height: 200px">
                <quill-editor
                  :content="queryForm.detail"
                  @onEditorChange="onEditorChangeServiceContent"
                />
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="position-button">
        <el-button type="info" @click="selectClassify">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">立即添加餐品</el-button>
      </div>
    </div>
    <!-- 第二步 -->
    <div v-else>
      <div
        style="padding: 80px 0px 0px 0px; margin: 0 auto; text-align: center"
      >
        <el-image
          src="http://admin.image.meijiangwl.com/chenggogn_img.png"
          style="width: 120px; height: 120px"
        />
        <div
          style="padding: 20px 0px 50px 0px; font-size: 20px; color: #333333"
        >
          餐品添加成功
        </div>
      </div>
      <div style="padding: 0px 0px 100px 0px; text-align: center">
        <el-button
          style="margin-right: 30px"
          type="primary"
          @click="goAddGoods"
        >
          继续添加餐品
        </el-button>
        <el-button
          style="margin-right: 30px"
          type="info"
          @click="selectClassify"
        >
          关闭添加商户窗口
        </el-button>
      </div>
    </div>
  </div>
</template>
<script>
  import { mapActions } from 'vuex'
  import { handleActivePath } from '@/utils/routes'
  import ImgUpload from './upload.vue'
  import QuillEditor from '@/components/editor/index'
  import { foodTypeList, addFood } from '@/api/food'

  export default {
    name: 'FoodTableAdd',
    components: {
      ImgUpload,
      QuillEditor,
    },
    data() {
      return {
        active: 1, // 步骤
        specs: [], // 分账模板规格
        queryForm: {
          categoryId: '', // 餐品类型ID
          name: '', // 餐品名称
          // price: '', // 价格
          // stock: '', // 库存数量
          imageUrl: '', // 图片URL
          // specification: '', // 规格
          description: '', // 餐品描述
          detail: '', // 餐品详情富文本
        },
        FoodTypeData: [], // 餐品类型数据
      }
    },
    created() {
      // 获取餐品类型
      this.getFoodType()
      // 添加属性
      // this.addSpecs()
    },
    methods: {
      ...mapActions({
        delVisitedRoute: 'tabs/delVisitedRoute',
      }),
      // 获取餐品类型
      async getFoodType() {
        this.loading = true
        const { code, data } = await foodTypeList({
          currentPage: 1, // 页码
          pageSize: 99, // 页数
          operType: 0, // 0查询，1导出
        })
        if (code == '00000') {
          this.FoodTypeData = data.data
        }
      },
      // 获取餐品图片
      getImageUrl(fileUrl) {
        console.log(fileUrl)
        this.queryForm.imageUrl = fileUrl
      },
      // 删除图片
      removeImgs(index) {
        if (index == 1) {
          this.queryForm.imageUrl = ''
        }
      },
      // 添加属性
      addSpecs() {
        let obj = {
          title: '辣度',
          value: [
            { name: '微辣', inventory: 2000 },
            { name: '中辣', inventory: 2000 },
            { name: '重辣', inventory: 2000 },
          ],
        }
        this.specs = [obj]
      },
      // 删除属性
      deleteSpecs(index) {
        this.specs.splice(index, 1)
      },
      // 添加属性值
      addSpecsValue(index) {
        let obj = { name: '', inventory: '' }
        this.specs[index].value.push(obj)
      },
      // 删除属性值
      deleteValue(index, i) {
        this.specs[index].value.splice(i, 1)
      },
      // 监听富文本框-餐品详情
      onEditorChangeServiceContent(content) {
        this.queryForm.detail = content
      },
      async goBack() {
        const detailPath = await handleActivePath(this.$route, true)
        await this.$router.push('/food/list')
        await this.delVisitedRoute(detailPath)
      },
      //初始化数据
      initData() {
        this.queryForm = {
          categoryId: '', // 餐品类型ID
          name: '', // 餐品名称
          // price: '', // 价格
          // stock: '', // 库存数量
          imageUrl: '', // 图片URL
          // specification: '', // 规格
          description: '', // 餐品描述
          detail: '', // 餐品详情富文本
        }
      },
      // 确认创建餐品
      async handleSubmit() {
        let flag = true
        if (this.queryForm.categoryId == '') {
          this.$notify.error({
            title: '温馨提示',
            message: '请选择餐品类型',
            offset: 180,
          })
          return false
        } else if (this.queryForm.name == '') {
          this.$notify.error({
            title: '温馨提示',
            message: '请输入餐品名称',
            offset: 180,
          })
          return false
        // } else if (this.queryForm.price == '') {
        //   this.$notify.error({
        //     title: '温馨提示',
        //     message: '请输入价格',
        //     offset: 180,
        //   })
        //   return false
        // } else if (this.queryForm.stock == '') {
        //   this.$notify.error({
        //     title: '温馨提示',
        //     message: '请输入库存',
        //     offset: 180,
        //   })
        //   return false
        } else {
          if (this.specs.length > 0) {
            for (let i = 0; i < this.specs.length; i++) {
              if (this.specs[i].title === '') {
                this.$notify.error({
                  title: '温馨提示',
                  message: `第${i + 1}条模板名称不能为空`,
                  offset: 180,
                })
                flag = false
                break
              }
            }
            let arr = this.specs
            for (let i = 0; i < arr.length; i++) {
              for (let j = 0; j < arr[i].value.length; j++) {
                if (arr[i].value[j].name === '') {
                  this.$notify.error({
                    title: '温馨提示',
                    message: `${arr[i].title}规格中第${j + 1}条名称不能为空`,
                    offset: 180,
                  })
                  flag = false
                  break
                }
                if (arr[i].value[j].inventory === '') {
                  this.$notify.error({
                    title: '温馨提示',
                    message: `${arr[i].title}规格中第${j + 1}条库存不能为空`,
                    offset: 180,
                  })
                  flag = false
                  break
                }
              }
            }
          }
          if (!flag) return false
          // this.queryForm.specification = JSON.stringify(this.specs)
          console.log(this.queryForm, '-------------')
          const { code, msg } = await addFood(this.queryForm)
          if (code == '00000') {
            this.$baseMessage(msg, 'success', 'vab-hey-message-success')
            this.active = 2
          }
        }
      },
      //继续添加商户
      async goAddGoods() {
        this.active = 1
        this.initData()
      },
      //关闭添加商户窗口，回到商户列表
      async selectClassify() {
        await this.delVisitedRoute(this.$route.path)
        this.$router.push({
          path: '/food/list',
          query: {},
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .specs-content {
    width: 800px;
    display: flex;
    flex-wrap: wrap;

    .specs-li {
      width: 100%;

      .header-content {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .header-left {
          display: flex;
          align-items: center;
        }
      }

      .value-content {
        display: flex;
        flex-direction: column;

        .value-li {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin: 5px 0px;
          .label {
            width: 150px;
            text-align: center;
          }
          .delete-value {
            margin-left: 10px;
          }
        }

        .add-specs-value {
          margin-top: 10px;
        }
      }
    }
  }
  .position-button {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 30px 0px;
  }
  .upload-img-box {
    padding: 0;
    margin: 0;
  }
  .upload-img {
    position: relative;
    box-sizing: border-box;
    display: inline-block;
    width: 148px;
    height: 148px;
    margin: 0 12px 12px 0;
    background-color: #fff;
    border: 1px solid #c0ccda;
    border-radius: 6px;
  }
  .upload-img .img {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
  .upload-img .close {
    position: absolute;
    top: -12px;
    right: -12px;
    width: 24px;
    height: 24px;
    cursor: pointer;
  }
  .textTips {
    padding: 0px 0px;
    font-size: 12px;
    color: #f56c6c;
  }
</style>
