import request from '@/utils/request'

/**
 * 商户列表
 * @param {*} data
 * @returns
 */
export async function allBusinessList(data) {
  return request({
    url: '/merchant-info/pageList',
    method: 'post',
    data,
  })
}

/**
 * 添加商户信息
 * @param {Object} data - 商户信息数据
 * @returns {Promise} - 返回请求结果
 */
export async function AddBusiness(data) {
  return request({
    url: '/merchant-info/save',
    method: 'post',
    data,
  })
}

/**
 * 获取商户信息
 * @param {Object} data - 请求数据
 * @returns {Promise} - 返回商户信息的响应
 */
export async function merchantInfo(data) {
  return request({
    url: '/merchant-info/get',
    method: 'post',
    data,
  })
}

/**
 * 更新商户信息
 * @param {Object} data - 请求数据
 * @returns {Promise} - 返回商户信息的响应
 */
export async function updateBusiness(data) {
  return request({
    url: '/merchant-info/update',
    method: 'post',
    data,
  })
}

/**
 * 分账模板接口
 * @param {Object} data - 包含模板数据的请求体。
 * @returns {Promise} 返回一个Promise对象。
 */
export async function splitTemplate(data) {
  return request({
    url: '/split-template/pageList',
    method: 'post',
    data,
  })
}

// 模板详情
export async function templateInfo(data) {
  return request({
    url: '/split-template/get',
    method: 'post',
    data,
  })
}

/**
 * 模板内容接口
 * @param {Object} data - 包含模板数据的请求体。
 * @returns {Promise} 返回一个Promise对象。
 */
export async function templateDetail(data) {
  return request({
    url: '/split-template-detail/pageList',
    method: 'post',
    data,
  })
}

// 新增分账模板接口
export async function addTemplate(data) {
  return request({
    url: '/split-template/save',
    method: 'post',
    data,
  })
}

// 新增分账模板内容比例
export async function addBatch(data) {
  return request({
    url: '/split-template-detail/batch-save',
    method: 'post',
    data,
  })
}

// 修改分账模板接口
export async function editTemplate(data) {
  return request({
    url: '/split-template/update',
    method: 'post',
    data,
  })
}

// 修改分账模板内容比例
export async function editBatch(data) {
  return request({
    url: '/split-template-detail/batch-update',
    method: 'post',
    data,
  })
}

// 删除分账模板
export async function deleteTemplate(data) {
  return request({
    url: '/split-template/delete',
    method: 'post',
    data,
  })
}

// 同步商户银行信息
export async function syncMerchantBank(data) {
  return request({
    url: '/merchant-info/syncMerchantBank',
    method: 'post',
    data,
  })
}

// 更新商户银行信息
export async function updateMerchantBank(data) {
  return request({
    url: '/merchant-info/updateMerchantBank',
    method: 'post',
    data,
  })
}

// 根据名称模糊搜索
export async function mccSearchByName(data) {
  return request({
    url: `/mcc/searchByName`,
    method: 'post',
    data
  })
}

// 查询商户银行信息
export async function queryMerchantInfo(data) {
  return request({
    url: `/merchant-info/queryMerchantInfo`,
    method: 'post',
    data
  })
}
