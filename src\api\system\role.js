import request from '@/utils/request'

//角色列表
export function doRoleList(data) {
  return request({
    url: '/role/pageList',
    method: 'post',
    data,
  })
}

//角色添加
export function doRoleAdd(data) {
  return request({
    url: '/role/save',
    method: 'post',
    data,
  })
}
//角色修改
export function doRoleUpdate(data) {
  return request({
    url: '/role/update',
    method: 'post',
    data,
  })
}

//角色删除
export function doRoleDelete(params) {
  return request({
    url: '/backend/role/delete',
    method: 'post',
    params,
  })
}

// 查询角色菜单列表
export function doQueryMenus(data) {
  return request({
    url: '/role/queryMenus',
    method: 'post',
    data,
  })
}

// 查询角色授权的菜单列表
export function doRoleMenuQuery(data) {
  return request({
    url: '/sys-menu/tree',
    method: 'post',
    data,
  })
}
//保存角色授权的菜单列表
export function doRoleMenuSave(data) {
  return request({
    url: '/role-menu/updateRoleMenus',
    method: 'post',
    data,
  })
}
