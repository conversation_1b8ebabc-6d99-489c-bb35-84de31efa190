import request from '@/utils/requests'

// DTU设备列表
export function dtuList(data) {
  return request({
    url: '/dtu/pageList',
    method: 'post',
    data,
  })
}

// 新增DTU设备
export function addDtu(data) {
  return request({
    url: '/dtu/save',
    method: 'post',
    data,
  })
}

// 修改DTU设备
export function editDtu(data) {
  return request({
    url: '/dtu/update',
    method: 'post',
    data,
  })
}

// 删除DTU设备
export function deleteDtu(data) {
  return request({
    url: '/dtu/delete',
    method: 'post',
    data,
  })
}

// 绑定DTU设备
export function bindDtu(data) {
  return request({
    url: '/dtu/bind',
    method: 'post',
    data,
  })
}

// 解绑DTU设备
export function unbindDtu(data) {
  return request({
    url: '/dtu/unbind',
    method: 'post',
    data,
  })
}
