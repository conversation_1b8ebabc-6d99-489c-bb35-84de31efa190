<template>
  <div class="index-container">
    <!-- 查询条件 -->
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form :inline="true" label-width="100px" :model="queryForm">
          <el-form-item label="餐品类型名称">
            <el-input
              v-model="queryForm.name"
              :clearable="true"
              placeholder="请输入餐品类型名称"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              type="primary"
              @click="handleQuery"
            >
              查 询
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <!-- 按钮 -->
      <vab-query-form-top-panel>
        <el-form :inline="true">
          <el-form-item label="快捷操作：">
            <el-button
              icon="el-icon-circle-plus-outline"
              type="primary"
              @click="handleAdd"
            >
              添加餐品类型
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <!-- 按钮 -->
    </vab-query-form>
    <!-- begin表格 -->
    <el-table ref="tableSort" v-loading="loading" border :data="tableData">
      <div slot="empty" style="margin: 50px 0">
        <img
          class="img"
          src="https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/image/1865665326720536578.png"
        />
      </div>
      <el-table-column align="center" label="序号">
        <template #default="scope">
          {{
            (queryForm.currentPage - 1) * queryForm.pageSize + scope.$index + 1
          }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="餐品类型名称">
        <template #default="{ row }">
          <div>{{ row.name }}</div>
        </template>
      </el-table-column>
      <!-- <el-table-column align="center" label="餐品类型编号">
        <template #default="{ row }">
          <div v-if="row.code">{{ row.code }}</div>
          <div v-else>暂无编号</div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="餐品类型排序">
        <template #default="{ row }">
          <div>{{ row.sortOrder }}</div>
        </template>
      </el-table-column> -->
      <el-table-column align="center" fixed="right" label="操作" width="200">
        <template #default="{ row }">
          <div>
            <el-link type="primary" @click="handleEdit(row)">编辑</el-link>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-if="queryForm.total > 10"
      background
      :current-page="queryForm.currentPage"
      :layout="layout"
      :page-size="queryForm.pageSize"
      :total="queryForm.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <!-- end表格 -->
    <!-- 新增DTU设备弹窗 -->
    <el-dialog
      center
      title="新增餐品类型"
      :visible.sync="addDialogVisible"
      width="30%"
    >
      <el-form label-position="right" label-width="120px" :model="form">
        <el-form-item label="餐品类型名称" required>
          <el-input
            v-model="form.name"
            :clearable="true"
            placeholder="请输入餐品类型名称"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleAddFoodType">
          {{ btnText }}
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { foodTypeList, addFoodType, updateFoodType } from '@/api/food'
  export default {
    name: 'FoodType',
    components: {},
    data() {
      return {
        addDialogVisible: false, // 新增餐品类型弹窗
        btnText: '确认新增', // 按钮文字
        queryForm: {
          name: '', // 餐品类型名称
          currentPage: 1, // 页码
          pageSize: 20, // 页数
          operType: 0, // 0查询，1导出
          total: 0, // 总条数
        },
        form: {
          id: '', // 餐品id
          name: '', // 餐品类型名称
        },
        loading: false, // 加载状态
        tableData: [], // 列表数据
        layout: 'total, sizes, prev, pager, next, jumper', // 分页样式
      }
    },
    computed: {},
    beforeMount() {},
    beforeDestroy() {},
    created() {
      this.$nextTick(() => {
        this.fetchData()
      })
    },
    methods: {
      // 新增餐品类型
      async handleAddFoodType() {
        if (!this.form.name) {
          this.$baseMessage(
            '请输入餐品类型名称',
            'error',
            'vab-hey-message-error'
          )
          return
        } else {
          let request = null
          if (this.requestType == 1) {
            request = addFoodType
          } else {
            request = updateFoodType
          }
          let { code, data } = await request(this.form)
          if (code == '00000' && data) {
            this.$baseMessage('操作成功', 'success', 'vab-hey-message-success')
            this.addDialogVisible = false
            this.handleQuery()
          }
        }
      },
      // 新增事件
      handleAdd() {
        this.btnText = '确认新增'
        this.form.name = ''
        this.requestType = 1
        this.addDialogVisible = true
      },
      // 查询
      handleQuery() {
        this.queryForm.currentPage = 1
        this.fetchData()
      },
      // 分页
      handleSizeChange(val) {
        this.queryForm.currentPage = val
        this.fetchData()
      },
      // 分页
      handleCurrentChange(val) {
        this.queryForm.currentPage = val
        this.fetchData()
      },
      // 列表查询
      async fetchData() {
        this.loading = true
        const { code, data } = await foodTypeList(this.queryForm)
        if (code == '00000') {
          this.tableData = data.data
          this.queryForm.total = data.paginator.totalRecord
        }
        this.loading = false
      },
      // 编辑
      handleEdit(row) {
        this.btnText = '确认编辑'
        this.form.id = row.id
        this.form.name = row.name
        this.requestType = 2
        this.addDialogVisible = true
      },
    },
  }
</script>

<style lang="scss" scoped>
  .link-padding {
    margin-left: 15px;
  }
  .table-column {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .table-column-price {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
</style>
