<template>
  <div class="uploading-container">
    <div class="upload-input-div">
      <el-button icon="md-arrow-up" type="primary">
        {{ content }}
        <i class="el-icon-upload el-icon--right"></i>
      </el-button>
      <p class="textTips2">{{ tips }}</p>
      <input
        accept="image/*"
        class="file-upload-input"
        type="file"
        @change="uploadFile"
      />
    </div>
  </div>
</template>

<script>
  import { postUploadFile } from '@/api/upload'
  export default {
    name: 'FileUpload',
    components: {},
    props: {
      content: {
        type: String,
        default: '上传图片',
      },
      tips: {
        type: String,
        default: '尺寸要求：最小 375*125 最大750*250',
      },
    },
    data() {
      return {
        imgsData: [],
        accept: 'image/gif, image/jpeg, image/png, image/jpg', //图片上传类型
        fileUrl: '',
      }
    },
    computed: {},
    beforeMount() {},
    beforeDestroy() {},
    created() {},
    methods: {
      //上传文件  图片
      uploadFile(event) {
        var that = this
        let img1 = event.target.files[0]
        let type = img1.type //文件的类型，判断是否是图片
        let size = img1.size //文件的大小，判断图片的大小

        if (that.accept.indexOf(type) == -1) {
          that.$message({
            message: '请选择gif,jpeg,png,jpg格式图片上传',
            type: 'warning',
          })
          return false
        } else if (size > 3145728) {
          that.$message({
            message: '请选择3M以内的图片上传',
            type: 'warning',
          })
          return false
        } else if (typeof FileReader === 'undefined') {
          that.$notify.error({
            title: '错误',
            message:
              '抱歉，你的浏览器不支持 FileReader，请使用现代浏览器操作！',
          })
        } else {
          this.$loading({ text: '图片上传中...' })
          // const Loading = this.$baseColorfullLoading(0, '图片上传中...')
          postUploadFile(img1).then((response) => {
            if (response.code == '00000') {
              var fileUrl = response.data.ossUrl
              that.$notify.success({
                title: '操作结果',
                message: '上传成功',
                offset: 100,
              })
              setTimeout(function () {
                that.$loading().close()
                // Loading.close()
              }, 500)
              that.$emit('upload-data', response.data)
              that.fileUrl = fileUrl
              that.imgsData.push(fileUrl)
              // that.$emit('upload-data', that.imgsData)
            } else {
              that.$notify.error({
                title: '温馨提示',
                desc: response.message,
              })
            }
          })
        }
      },
      //删除图片
      handleRemove(index) {
        let that = this
        this.$baseConfirm('你确定要删除当前项吗', null, () => {
          this.imgsData.splice(index, 1)
          this.$baseMessage('删除成功', 'success', 'vab-hey-message-success')
          this.$emit('upload-data', that.imgsData)
        })
      },
      //预览图片
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
    },
  }
</script>

<style lang="scss" scoped>
  .upload-input-div {
    position: relative;
    padding: 0px 0px 0px 0px;
  }

  .file-upload-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 180px;
    height: 50px;
    margin-top: 0px;
    cursor: pointer;
    opacity: 0;
  }

  .textTips {
    padding: 0px 0px;
    font-size: 12px;
    color: #d40000;
  }

  .textTips2 {
    padding: 5px 0px;
    font-size: 12px;
    line-height: 14px;
    color: #969799;
  }
  .upload-img-box {
    padding: 0;
    margin: 0;
  }
  .upload-img {
    position: relative;
    box-sizing: border-box;
    display: inline-block;
    width: 148px;
    height: 148px;
    margin: 0 12px 12px 0;
    background-color: #fff;
    border: 1px solid #c0ccda;
    border-radius: 6px;
  }
  .upload-img .img {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }

  .upload-img .close {
    position: absolute;
    top: -12px;
    right: -12px;
    width: 24px;
    height: 24px;
    cursor: pointer;
  }
</style>
