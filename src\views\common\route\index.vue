<template>
  <div class="index-container">
    <vab-query-form>
      <vab-query-form-left-panel :span="24">
        <el-button icon="el-icon-plus" type="primary" @click="handleAdd">
          添加菜单
        </el-button>
      </vab-query-form-left-panel>
    </vab-query-form>
    <!-- begin 表格 -->
    <el-table v-loading="loading" :data="routeList" :default-expand-all="isExpandAll" row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
      <el-table-column label="菜单名称">
        <template slot-scope="scope">
          <vab-icon :icon="scope.row.icon" />
          {{ scope.row.name }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="排序" prop="sort" width="160" />
      <el-table-column label="路由地址" prop="path" />
      <el-table-column label="组件地址" prop="component" />
      <el-table-column align="center" label="badge" prop="meta.badge" width="100" />
      <el-table-column align="center" label="是否显示" width="180">
        <template #default="{ row }">
          <el-tag type="success">
            {{ row.hidden | hiddenFilter }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="240">
        <template slot-scope="scope">
          <el-link class="line-padding" type="success" @click="handleUpdate(scope.row)">
            <i class="el-icon-edit"></i>
            修改
          </el-link>
          <!-- <el-link class="line-padding" @click="handleDetails(scope.row)">
            <i class="el-icon-view"></i>
            详情
          </el-link> -->
          <el-link type="danger" @click="handleDelete(scope.row)">
            <i class="el-icon-delete"></i>
            删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <!-- end 表格 -->
    <!-- 其他组件 -->
    <route-table ref="edit" @fetch-data="fetchData" />
    <route-detail ref="detail" />
    <!-- 其他组件 -->
  </div>
</template>
<script>
import { getList, doRouteDelete } from '@/api/router'
import RouteTable from './components/RouteTable'
import RouteDetail from './components/RouteDetail'
export default {
  name: 'Route',
  components: {
    RouteTable,
    RouteDetail,
  },
  filters: {
    statusFilter(status) {
      const statusMap = {
        0: '正常',
        1: '禁用',
      }
      return statusMap[status]
    },
    hiddenFilter(hidden) {
      const statusMap = hidden === 1 ? '显示' : '隐藏'
      return statusMap
    },
  },
  data() {
    return {
      queryForm: {
        currentPage: 1, // 页码
        pageSize: 200, // 页数
        operType: 0, // 0查询，1导出
      },
      routeList: [],
      loading: false,
      // 是否展开，默认全部折叠
      isExpandAll: true,
    }
  },
  computed: {},
  beforeMount() { },
  beforeDestroy() { },
  created() {
    this.fetchData()
  },
  methods: {
    //菜单列表
    async fetchData() {
      this.loading = true
      const { data } = await getList(this.queryForm)
      this.routeList = data
      setTimeout(() => {
        this.loading = false
      }, 500)
    },
    //添加菜单
    handleAdd() {
      this.$refs['edit'].showEdit()
    },
    //修改菜单
    handleUpdate(row) {
      this.$refs['edit'].showEdit(row)
    },
    //显示菜单详情
    handleDetails(row) {
      this.$refs['detail'].show(row)
    },
    //删除
    handleDelete(row) {
      if (row.id) {
        this.$baseConfirm('你确定要删除当前项吗', null, async () => {
          const { message } = await doRouteDelete({ ids: row.id })
          this.$baseMessage(message, 'success', 'vab-hey-message-success')
          await this.fetchData()
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.line-padding {
  padding-right: 10px;
}
</style>
