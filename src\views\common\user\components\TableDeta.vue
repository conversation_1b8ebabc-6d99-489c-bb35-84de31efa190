<template>
  <el-drawer
    :direction="direction"
    size="1000px"
    :title="title"
    :visible.sync="dialogFormVisible"
  >
    <div style="padding: 0px 60px">
      <el-form ref="form" label-width="100px">
        <el-form-item label="登录账号：">
          <span>{{ form.userName }}</span>
        </el-form-item>
        <el-form-item label="所属角色：">
          <span>{{ form.remark }} {{ form.roleName }}</span>
        </el-form-item>
        <el-form-item label="真实姓名：">
          <span>{{ form.realName }}</span>
        </el-form-item>
        <el-form-item label="头像：">
          <div class="image-box">
            <img class="image" :class="active" :src="form.logo" />
          </div>
        </el-form-item>
        <el-form-item label="性别：">
          <el-radio-group v-model="form.gender">
            <el-radio :label="1">男</el-radio>
            <el-radio :label="2">女</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="手机号码：">
          <span>{{ form.mobile }}</span>
        </el-form-item>
        <el-form-item label="邮箱：">
          <span>{{ form.email }}</span>
        </el-form-item>
        <el-form-item label="创建时间：">
          <span>{{ form.createTime }}</span>
        </el-form-item>
        <el-form-item label="状态：">
          <span>{{ form.state }}</span>
        </el-form-item>
      </el-form>
      <div style="text-align: center">
        <el-button @click="close">关 闭</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
  export default {
    name: 'TableDeta',
    data() {
      return {
        type: 1, //1添加  2编辑
        form: {
          gender: 1,
          mobile: '',
          pwd: '',
          realName: '',
          roleId: '',
          saleDeptId: 0, //忽视这个字的
          userName: '',
          email: '',
          logo: '',
        },
        title: '',
        clickModel: false,
        direction: 'ltr',
        dialogFormVisible: false,
        active: 0,
        options: [],
      }
    },
    created() {},
    methods: {
      showDetails(row) {
        this.title = '显示详情信息'
        // console.log(row)
        // let obj = Object.assign({}, row)
        // console.log(obj)
        this.form = Object.assign({}, row)
        //显示 dialog
        this.dialogFormVisible = true
      },
      close() {
        this.dialogFormVisible = false
      },
    },
  }
</script>
<style lang="scss" scoped>
  .image-box {
    .image {
      width: 45px;
      height: 45px;
      margin-right: 10px;
      margin-bottom: 10px;
      cursor: pointer;
      border-radius: 100%;
    }
    .active {
      width: 48px;
      height: 48px;
      border: 3px solid #f34d37;
    }
  }
</style>
