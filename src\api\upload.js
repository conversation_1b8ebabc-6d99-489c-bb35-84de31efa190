import axios from 'axios'
import store from '@/store'
import { baseURL } from '@/config'
// 表单形式上传图片
export const postUploadFile = (file) => {
  let params = new FormData()
  params.append('file', file)
  let token = ''
  let storeToken = store.getters['user/token']
  if (storeToken && storeToken != '') {
    token = '' + storeToken
  }
  return axios({
    method: 'post',
    url: baseURL + '/file/common/upload/both',
    data: params,
    headers: {
      'Content-Type': 'multipart/form-data',
      token,
    },
  }).then(function (res) {
    const { data } = res
    return data
  })
}

// 表单形式上传图片
export const postUploadFiles = (file) => {
  let params = new FormData()
  params.append('file', file)
  let token = ''
  let storeToken = store.getters['user/token']
  if (storeToken && storeToken != '') {
    token = '' + storeToken
  }
  return axios({
    method: 'post',
    url: baseURL + '/file/common/image/upload',
    data: params,
    headers: {
      'Content-Type': 'multipart/form-data',
      token,
    },
  }).then(function (res) {
    const { data } = res
    return data
  })
}
