/**
 * @description router全局配置，如有必要可分文件抽离，其中asyncRoutes只有在intelligence模式下才会用到，pro版只支持remixIcon图标，具体配置请查看vip群文档
 */
import Vue from "vue";
import VueRouter from "vue-router";
import Layout from "@/vab/layouts";
import { publicPath, routerMode } from "@/config";

Vue.use(VueRouter);
export const constantRoutes = [
  {
    path: "/login",
    component: () => import("@/views/login"),
    meta: {
      hidden: true,
    },
  },
  {
    path: "/register",
    component: () => import("@/views/register"),
    meta: {
      hidden: true,
    },
  },
  {
    path: "/403",
    name: "403",
    component: () => import("@/views/403"),
    meta: {
      hidden: true,
    },
  },
  {
    path: "/404",
    name: "404",
    component: () => import("@/views/404"),
    meta: {
      hidden: true,
    },
  },
];

export const asyncRoutes = [
  {
    path: "/",
    name: "Root",
    component: Layout,
    meta: {
      title: "首页",
      icon: "home-2-line",
      breadcrumbHidden: true,
    },
    children: [
      {
        path: "index",
        name: "Index",
        component: () => import("@/views/index"),
        meta: {
          title: "首页",
          icon: "home-2-line",
          noClosable: true,
        },
      },
    ],
  },
  {
    path: "/business/list",
    name: "Business",
    component: Layout,
    meta: {
      title: "商户",
      icon: "archive-drawer-line",
      breadcrumbHidden: true,
    },
    children: [
      {
        path: "/business/list",
        name: "BusinessIndex",
        component: () => import("@/views/business/index"),
        meta: {
          title: "商户列表",
          icon: "file-list-line",
        },
      },
      {
        path: "/business/cents",
        name: "BusinessCents",
        component: () => import("@/views/business/cents/index"),
        meta: {
          title: "分账模板",
          icon: "money-cny-circle-line",
        },
      },
    ],
  },
  {
    path: "/device/list",
    name: "Device",
    component: Layout,
    meta: {
      title: "设备",
      icon: "device-line",
      breadcrumbHidden: true,
    },
    children: [
      {
        path: "/device/list",
        name: "DeviceIndex",
        component: () => import("@/views/device/index"),
        meta: {
          title: "设备列表",
          icon: "file-list-line",
        },
      },
      {
        path: "/device/fault",
        name: "故障列表",
        component: () => import("@/views/device/fault"),
        meta: {
          title: "故障列表",
          icon: "file-list-line",
        },
      },
      {
        path: "/device/delivery",
        name: "配送员列表",
        component: () => import("@/views/device/delivery"),
        meta: {
          title: "配送员列表",
          icon: "file-list-line",
        },
      },
      {
        path: "/device/maintenance",
        name: "维保员列表",
        component: () => import("@/views/device/maintenance"),
        meta: {
          title: "维保员列表",
          icon: "file-list-line",
        },
      },
      {
        path: "/device/add",
        name: "添加设备",
        component: () => import("@/views/device/components/TableAdd"),
        meta: {
          title: "添加设备",
          hidden: true,
        },
      },
      {
        path: "/device/edit",
        name: "DeviceEdit",
        component: () => import("@/views/device/components/TableEdit"),
        meta: {
          title: "修改设备",
          hidden: true,
        },
      },
    ],
  },
  {
    path: "/iot/list",
    name: "Iot",
    component: Layout,
    meta: {
      title: "IOT",
      icon: "router-line",
      breadcrumbHidden: true,
    },
    children: [
      {
        path: "/iot/list",
        name: "IotIndex",
        component: () => import("@/views/iot/index"),
        meta: {
          title: "DTU设备",
          icon: "file-list-line",
        },
      },
    ],
  },
  {
    path: "/food/list",
    name: "Food",
    component: Layout,
    meta: {
      title: "餐品",
      icon: "cake-3-line",
      breadcrumbHidden: true,
    },
    children: [
      {
        path: "/food/list",
        name: "FoodIndex",
        component: () => import("@/views/food/index"),
        meta: {
          title: "餐品列表",
          icon: "file-list-line",
        },
      },
      {
        path: "/food/type",
        name: "FoodType",
        component: () => import("@/views/food/type"),
        meta: {
          title: "餐品类型",
          icon: "file-list-line",
        },
      },
      {
        path: "/food/add",
        name: "FoodAdd",
        component: () => import("@/views/food/components/TableAdd"),
        meta: {
          title: "新增餐品",
          hidden: true,
          icon: "file-list-line",
        },
      },
      {
        path: "/food/edit",
        name: "FoodEdit",
        component: () => import("@/views/food/components/TableEdit"),
        meta: {
          title: "修改餐品",
          hidden: true,
          icon: "file-list-line",
        },
      },
    ],
  },
  {
    path: "/order/list",
    name: "Order",
    component: Layout,
    meta: {
      title: "订单",
      icon: "file-list-2-line",
      breadcrumbHidden: true,
    },
    children: [
      {
        path: "/order/list",
        name: "OrderIndex",
        component: () => import("@/views/order/index"),
        meta: {
          title: "订单列表",
          icon: "file-list-line",
        },
      },
    ],
  },
  {
    path: "/fund/list",
    name: "Fund",
    component: Layout,
    meta: {
      title: "资金",
      icon: "money-cny-circle-line",
      breadcrumbHidden: true,
    },
    children: [
      {
        path: "/fund/list",
        name: "FundIndex",
        component: () => import("@/views/fund/index"),
        meta: {
          title: "支付订单",
          icon: "file-list-line",
        },
      },
    ],
  },
  {
    path: "/user/list",
    name: "User",
    component: Layout,
    meta: {
      title: "用户",
      icon: "user-3-line",
      breadcrumbHidden: true,
    },
    children: [
      {
        path: "/user/list",
        name: "UserIndex",
        component: () => import("@/views/user/index"),
        meta: {
          title: "用户列表",
          icon: "file-list-line",
        },
      },
    ],
  },
  {
    path: "/marketing/coupon",
    name: "Marketing",
    component: Layout,
    meta: {
      title: "营销",
      icon: "volume-up-line",
      breadcrumbHidden: true,
    },
    children: [
      {
        path: "/marketing/coupon",
        name: "Marketing",
        component: () => import("@/views/marketing/coupon"),
        meta: {
          title: "优惠券管理",
          icon: "coupon-3-line",
        },
        children: [
          {
            path: "/marketing/coupon",
            name: "MarketingCoupon",
            component: () => import("@/views/marketing/coupon"),
            meta: {
              title: "普通券",
              icon: "coupon-2-line",
            },
          },
          {
            path: "/marketing/fission",
            name: "MarketingFission",
            component: () => import("@/views/marketing/fission"),
            meta: {
              title: "裂变券",
              icon: "stackshare-line",
            },
          },
        ],
      },
      {
        path: "/marketing/promotion/activity",
        name: "Promotion",
        component: () => import("@/views/marketing/activity"),
        meta: {
          title: "推广",
          icon: "thumb-up-line",
        },
        children: [
          {
            path: "/marketing/promotion/activity",
            name: "MarketingActivity",
            component: () => import("@/views/marketing/activity"),
            meta: {
              title: "活动管理",
              icon: "gift-line",
            },
          },
        ],
      },
    ],
  },
  {
    path: "/system/role",
    name: "System",
    component: Layout,
    meta: {
      title: "系统",
      icon: "apps-line",
      breadcrumbHidden: true,
    },
    children: [
      // {
      //   path: 'user',
      //   name: 'UserList',
      //   component: () => import('@/views/common/user/index'),
      //   meta: {
      //     title: '账户管理',
      //     icon: 'user-3-line',
      //   },
      // },
      {
        path: "/system/role",
        name: "Role",
        component: () => import("@/views/common/role/index"),
        meta: {
          title: "角色管理",
          icon: "computer-line",
        },
      },
      {
        path: "/system/route",
        name: "Menus",
        component: () => import("@/views/common/route/index"),
        meta: {
          title: "菜单管理",
          icon: "building-fill",
        },
      },
      {
        path: "/system/rule",
        name: "Rule",
        component: () => import("@/views/common/rule/index"),
        meta: {
          title: "协议配置",
          icon: "file-word-fill",
        },
      },
    ],
  },
  {
    path: "*",
    redirect: "/404",
    meta: {
      hidden: true,
    },
  },
];

const router = createRouter();

function fatteningRoutes(routes) {
  return routes.flatMap((route) => {
    return route.children ? fatteningRoutes(route.children) : route;
  });
}

export function resetRouter(routes = constantRoutes) {
  routes.map((route) => {
    if (route.children) {
      route.children = fatteningRoutes(route.children);
    }
  });
  router.matcher = createRouter(routes).matcher;
}

function createRouter(routes = constantRoutes) {
  return new VueRouter({
    base: publicPath,
    mode: routerMode,
    scrollBehavior: () => ({
      y: 0,
    }),
    routes: routes,
  });
}

const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
};

export default router;
