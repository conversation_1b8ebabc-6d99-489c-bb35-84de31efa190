<template>
  <!-- 查看详情 -->
  <div class="index-container">
    <el-drawer :direction="direction" :size="1300" :visible.sync="drawer">
      <div class="drawer-container">
        <!-- 添加配送员负责的设备列表 -->
        <div v-if="currentDeliveryman" class="device-list-container">
          <div class="device-list-header">
            <h3>负责的设备</h3>
            <p>配送员：{{ currentDeliveryman.name }}</p>
          </div>
          <el-table :data="deviceList" border style="width: 100%">
            <el-table-column prop="name" label="设备名称">
              <template #default="{ row }">
                <span>设备{{ row.deviceId }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="deviceId" label="设备编号">
              <template #default="{ row }">
                <span>No.{{ row.deviceId }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-button type="danger" size="mini" @click="handleUnbind(scope.row)">解除绑定</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-drawer>

    <!-- 解绑确认对话框 -->
    <el-dialog title="确认解绑" :visible.sync="unbindDialogVisible" width="30%">
      <span>确定要解除设备 {{ currentDevice.deviceId || '' }} 的绑定吗？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="unbindDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmUnbind">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { allocationDevices, unbindDevice } from '@/api/device'
export default {
  name: 'DTableSelect',
  components: {},
  data() {
    return {
      drawer: false, // 抽屉显示状态
      direction: 'rtl', // 抽屉方向
      labelPosition: 'right', // label对齐方式
      info: {}, // 设备详情
      currentDeliveryman: null, // 当前配送员
      deviceList: [], // 配送员负责的设备列表
      unbindDialogVisible: false, // 解绑确认对话框
      currentDevice: {} // 当前选中的设备
    }
  },
  methods: {
    show(row) {
      // 保存当前配送员信息
      this.currentDeliveryman = row;
      // 获取设备详情
      this.getDeviceDetails(row.id)
    },
    // 获取设备详情
    async getDeviceDetails(id) {
      try {
        const res = await allocationDevices({
          id: id
        });
        if (res.code === '00000') {
          this.deviceList = res.data;
          this.drawer = true;
        } else {
          this.$message.error(res.msg);
        }
      } catch (error) {
        console.error('获取设备详情失败:', error);
        this.$message.error('获取设备详情失败');
      }
    },
    close() {
      this.drawer = false;
      this.currentDeliveryman = null;
      this.deviceList = [];
    },
    handleClose(done) {
      done()
    },
    // 处理解绑设备
    handleUnbind(device) {
      this.currentDevice = device;
      this.unbindDialogVisible = true;
    },
    // 确认解绑设备
    async confirmUnbind() {
      try {
        // 调用API更新设备列表
        const params = {
          deliverymanId: this.currentDeliveryman.id,
          deviceId: this.currentDevice.deviceId
        };
        const res = await unbindDevice(params);

        if (res.code === '00000') {
          this.$message.success('设备解绑成功');
          // 更新本地设备列表
          this.getDeviceDetails(this.currentDeliveryman.id)
          this.unbindDialogVisible = false;
          // 通知父组件刷新列表
          this.$emit('refresh-list');
        } else {
          this.$message.error('设备解绑失败');
        }
      } catch (error) {
        console.error('设备解绑失败:', error);
        this.$message.error('设备解绑失败');
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// ... existing styles ...

.device-list-container {
  margin-top: 20px;
  padding: 0 20px;
}

.device-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  h3 {
    font-size: 16px;
    margin: 0;
    color: #409EFF;
  }

  p {
    font-size: 16px;
    margin: 0;
    font-weight: bold;
  }
}
</style>
