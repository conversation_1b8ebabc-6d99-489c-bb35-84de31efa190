<template>
  <div class="system-role-container">
    <vab-query-form>
      <vab-query-form-left-panel :span="24">
        <el-button icon="el-icon-plus" type="primary" @click="handleAdd">
          添加角色
        </el-button>
      </vab-query-form-left-panel>
    </vab-query-form>
    <!-- 角色表格 -->
    <el-table ref="tableSort" v-loading="listLoading" border :data="tableData">
      <el-table-column align="center" label="序号" prop="id" width="100">
        <template #default="scope">
          {{
        (queryForm.currentPage - 1) * queryForm.pageSize + scope.$index + 1
      }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="ID" prop="id" width="150" />
      <el-table-column align="center" label="父ID" prop="pid" width="150" />
      <el-table-column align="center" label="角色名称" prop="name" />
      <el-table-column align="center" label="角色编码" prop="code" />
      <el-table-column align="center" label="角色描述" prop="info" />
      <el-table-column align="center" label="操作" width="160">
        <template #default="{ row }">
          <el-button type="text" v-permissions="{ permission: ['user:write'] }"
            @click="handleMenus(row)">菜单权限</el-button>
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <!-- <el-button type="text" @click="handleDelete(row)">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <!-- 角色表格 -->
    <!-- 其他子组件 -->
    <table-edit ref="edit" @fetch-data="fetchData" />
    <role-menus ref="menus" />
    <!-- 其他子组件 -->
  </div>
</template>
<script>
import { doRoleList, doRoleDelete } from '@/api/system/role'
import TableEdit from './components/TableEdit'
import RoleMenus from './components/RoleMenus'
export default {
  name: 'Role',
  components: {
    TableEdit,
    RoleMenus,
  },
  filters: {
    statusFilter(status) {
      const statusMap = {
        0: '正常',
        1: '禁用',
      }
      return statusMap[status]
    },
  },
  data() {
    return {
      tableData: [],
      listLoading: true,
      queryForm: {
        currentPage: 1, // 页码
        pageSize: 200, // 页数
        operType: 0, // 0查询，1导出
      },
    }
  },
  computed: {},
  beforeMount() { },
  beforeDestroy() { },
  created() {
    this.fetchData()
  },
  methods: {
    //分页
    handleSizeChange(val) {
      this.queryForm.currentPage = val
      this.fetchData()
    },
    //分页
    handleCurrentChange(val) {
      this.queryForm.currentPage = val
      this.fetchData()
    },
    //列表
    async fetchData() {
      this.listLoading = true
      const {
        data: { data },
      } = await doRoleList(this.queryForm)
      this.tableData = data
      this.listLoading = false
    },
    //添加角色
    handleAdd() {
      this.$refs['edit'].showEdit()
    },
    //编辑账号
    handleEdit(row) {
      this.$refs['edit'].showEdit(row)
    },
    //删除账号
    handleDelete(row) {
      if (row.id) {
        console.log('当前的Pro:' + row.id)
        this.$baseConfirm('你确定要删除当前项吗', null, async () => {
          const { message } = await doRoleDelete({ id: row.id })
          this.$baseMessage(message, 'success', 'vab-hey-message-success')
          await this.fetchData()
        })
      }
    },
    //显示菜单权限
    handleMenus(row) {
      this.$refs['menus'].show(row)
    },
  },
}
</script>

<style lang="scss" scoped></style>
