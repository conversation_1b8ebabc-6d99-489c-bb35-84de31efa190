import request from "@/utils/request";

/**
 * 设备列表
 * @param {*} data
 * @returns
 */
export async function deviceList(data) {
  return request({
    url: "/device/pageList",
    method: "post",
    data,
  });
}

/**
 * 设备列表
 * @param {*} data
 * @returns
 */
export async function setDeviceStatus(data) {
  return request({
    url: `/device/update/status`,
    method: "post",
    data,
  });
}

/**
 * 获取所有商户的ID和名称
 * @param {Object} data - 请求所需的数据对象
 * @returns {Promise} 返回一个包含所有商户ID和名称的Promise对象
 */
export async function getAllMerchantIdsAndNames(data) {
  return request({
    url: "/merchant-info/getAllMerchantIdsAndNames",
    method: "post",
    data,
  });
}

// 所有菜品名称
export async function allFoodList(data) {
  return request({
    url: "/dish/pageList",
    method: "post",
    data,
  });
}

// 新增设备
export async function addDevice(data) {
  return request({
    url: "/device/save",
    method: "post",
    data,
  });
}

// 设备详情
export async function deviceDetails(data) {
  return request({
    url: "/device/get",
    method: "post",
    data,
  });
}

// 修改设备
export async function editDetails(data) {
  return request({
    url: "/device/update",
    method: "post",
    data,
  });
}

// 设备故障列表
export async function faultList(data) {
  return request({
    url: "/fault-type/pageList",
    method: "post",
    data,
  });
}

// 设备故障记录列表
export async function faultRecordList(data) {
  return request({
    url: "/fault-record/pageList",
    method: "post",
    data,
  });
}

// 生成设备访问二维码
export async function getDeviceQrCode(data) {
  return request({
    url: "/app/device/getDeviceQrCode",
    method: "post",
    data,
  });
}

// 配送员列表
export async function deliveryman(data) {
  return request({
    url: "/admin/deliveryman/page",
    method: "post",
    data,
  });
}

// 添加配送员
export async function addDeliveryMan(data) {
  return request({
    url: "/admin/deliveryman/add",
    method: "post",
    data,
  });
}

// 修改配送员
export async function updateDeliveryMan(data) {
  return request({
    url: "/admin/deliveryman/update",
    method: "post",
    data,
  });
}

// 删除配送员
export async function deleteDeliveryMan(data) {
  return request({
    url: "/admin/deliveryman/delete",
    method: "post",
    data,
  });
}

// 未分配的设备列表
export async function unassignedDevices(data) {
  return request({
    url: "/admin/deliveryman/unassigned-devices",
    method: "post",
    data,
  });
}

// 已分配设备列表
export async function allocationDevices(data) {
  return request({
    url: "/admin/deliveryman/devices",
    method: "post",
    data,
  });
}

// 分配设备给配送员
export async function assignDevices(data) {
  return request({
    url: "/admin/deliveryman/assign-devices",
    method: "post",
    data,
  });
}

// 解绑设备
export async function unbindDevice(data) {
  return request({
    url: "/admin/deliveryman/unbind-device",
    method: "post",
    data,
  });
}

// 查询配送员的配送记录
export async function deliveryRecords(data) {
  return request({
    url: "/admin/deliveryman/delivery-records",
    method: "post",
    data,
  });
}

// 分页查询维保员
export async function maintenanceList(data) {
  return request({
    url: "/admin/maintenance-personnel/page",
    method: "post",
    data,
  });
}

// 添加维保员
export async function addMaintenance(data) {
  return request({
    url: "/admin/maintenance-personnel/add",
    method: "post",
    data,
  });
}

// 更新维保员
export async function updateMaintenance(data) {
  return request({
    url: "/admin/maintenance-personnel/update",
    method: "post",
    data,
  });
}

// 删除维保员
export async function deleteMaintenance(data) {
  return request({
    url: "/admin/maintenance-personnel/delete",
    method: "post",
    data,
  });
}
