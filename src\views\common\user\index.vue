<template>
  <div class="system-user-container">
    <vab-query-form>
      <!-- <vab-query-form-top-panel>
        <el-form ref="form" :inline="true" label-width="88px" :model="queryForm" @submit.native.prevent>
          <el-form-item label="用户名">
            <el-input v-model="queryForm.userName" placeholder="请输入用户名" />
          </el-form-item>
          <el-form-item label="姓名">
            <el-input v-model="queryForm.realName" placeholder="请输入姓名" />
          </el-form-item>
          <el-form-item label="手机号码">
            <el-input v-model="queryForm.mobile" placeholder="请输入手机号码" />
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" native-type="submit" type="primary" @click="handleQuery">
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel> -->
      <vab-query-form-left-panel :span="24">
        <el-button icon="el-icon-plus" type="primary" @click="handleAdd">
          添加账号
        </el-button>
      </vab-query-form-left-panel>
    </vab-query-form>
    <!-- begin 表格 -->
    <el-table ref="tableSort" v-loading="listLoading" border :data="tableData">
      <el-table-column align="center" label="ID编号" prop="id" width="65" />
      <el-table-column align="center" label="登录账号" prop="userName" />

      <el-table-column align="center" label="用户姓名" prop="realName" show-overflow-tooltip />
      <el-table-column align="center" label="头像" width="120">
        <template #default="{ row }">
          <el-image :src="row.logo" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="所属角色" prop="remark" width="120" />
      <el-table-column align="center" label="手机号码" prop="mobile" width="150" />
      <el-table-column align="center" label="创建时间" prop="createTime" width="165" />
      <el-table-column align="center" label="操作" width="130">
        <template #default="{ row }">
          <p>
            <el-link type="primary" @click="handleEdit(row)">编辑</el-link>
          </p>
          <p>
            <el-link type="success" @click="handlePwd(row)">重置密码</el-link>
          </p>
          <p>
            <!-- <el-link @click="handleDetail(row)">详情</el-link> -->
          </p>
          <p>
            <el-link type="danger" @click="handleDelete(row)">删除</el-link>
          </p>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background :current-page="queryForm.currentPage" :layout="layout" :page-size="queryForm.pageSize"
      :total="queryForm.total" @current-change="handleCurrentChange" @size-change="handleSizeChange" />
    <!-- end 表格 -->
    <!-- begin 其他子组件 -->
    <table-edit ref="edit" @fetch-data="fetchData" />
    <table-deta ref="details" />
    <!-- end 其他子组件 -->
    <!-- 密码输入框 -->
    <el-dialog title="重置密码" :visible.sync="dialogVisible" width="500px">
      <div>
        <el-form ref="formPwd" label-width="100px" :model="form">
          <el-form-item label="登录账户：">
            <span>{{ form.userName }}</span>
          </el-form-item>
          <el-form-item label="新密码：">
            <el-input v-model="form.pwd" />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="savePwd">确定重置</el-button>
      </span>
    </el-dialog>
    <!-- 密码输入框 -->
  </div>
</template>
<script>
import {
  doAccountList,
  doAccountDelete,
  doAccountUpdatePwd,
} from '@/api/system/user'
import TableEdit from './components/TableEdit'
import TableDeta from './components/TableDeta'
export default {
  name: 'Index',
  components: {
    TableEdit,
    TableDeta,
  },
  data() {
    return {
      fold: false,
      tableData: [],
      listLoading: true,
      layout: 'total, sizes, prev, pager, next, jumper',
      queryForm: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
        operType: 0
      },
      dialogVisible: false, //重置密码操作框
      form: {
        id: 0, //重置密码输入框
        pwd: '',
        userName: '',
      },
    }
  },
  computed: {},
  beforeMount() { },
  beforeDestroy() { },
  created() {
    this.fetchData()
  },
  methods: {
    //展开 合并
    handleFold() {
      this.fold = !this.fold
    },
    //查询
    handleQuery() {
      this.queryForm.currentPage = 1
    },
    //分页
    handleSizeChange(val) {
      this.queryForm.pageSize = val
      this.fetchData()
    },
    //分页
    handleCurrentChange(val) {
      this.queryForm.pageSize = val
      this.fetchData()
    },
    //列表
    async fetchData() {
      this.listLoading = true
      const { data } = await doAccountList(this.queryForm)
      this.tableData = data.data
      this.queryForm.total = data.paginator.totalRecord
      this.listLoading = false
    },
    //添加账号
    handleAdd() {
      this.$refs['edit'].showEdit()
    },
    //编辑账号
    handleEdit(row) {
      this.$refs['edit'].showEdit(row)
    },
    //删除账号
    handleDelete(row) {
      if (row.id) {
        this.$baseConfirm('你确定要删除当前项吗', null, async () => {
          const { message } = await doAccountDelete({ ids: row.id })
          this.$baseMessage(message, 'success', 'vab-hey-message-success')
          await this.fetchData()
        })
      }
    },
    //显示详情
    handleDetail(row) {
      this.$refs['details'].showDetails(row)
    },
    //重置密码  显示重置密码输入框
    handlePwd(row) {
      this.form.id = row.id
      this.form.userName = row.userName
      this.dialogVisible = true
    },
    //提交密码
    async savePwd() {
      if (this.form.pwd.length < 5) {
        this.$notify({
          title: '温馨提示',
          message: '新密码长度不能低于6位数！',
          type: 'warning',
          offset: 180,
        })
      } else {
        const { message } = await doAccountUpdatePwd(this.form)

        this.$baseMessage(message, 'success', 'vab-hey-message-success')
        this.dialogVisible = false
        this.$refs['formPwd'].resetFields()
      }
    },
  },
}
</script>

<style lang="scss" scoped></style>
