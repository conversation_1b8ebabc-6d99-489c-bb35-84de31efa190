/**
 * @description 路由拦截状态管理，目前两种模式：all模式与intelligence模式，其中partialRoutes是菜单暂未使用
 */
import Vue from "vue";
import { asyncRoutes, constantRoutes, resetRouter } from "@/router";
import { getMenuList } from "@/api/router";
import { convertRouter, filterRoutes } from "@/utils/routes";
import { authentication, rolesControl } from "@/config";
import { isArray } from "@/utils/validate";

const state = () => ({
  routes: [],
  activeName: "",
});
const getters = {
  routes: (state) => state.routes,
  activeName: (state) => state.activeName,
};
const mutations = {
  /**
   * @description 多模式设置路由
   * @param {*} state
   * @param {*} routes
   */
  setRoutes(state, routes) {
    state.routes = routes;
  },
  /**
   * @description 修改Meta
   * @param {*} state
   * @param options
   */
  changeMenuMeta(state, options) {
    function handleRoutes(routes) {
      return routes.map((route) => {
        if (route.name === options.name)
          Object.assign(route.meta, options.meta);
        if (route.children && route.children.length)
          route.children = handleRoutes(route.children);
        return route;
      });
    }
    state.routes = handleRoutes(state.routes);
  },
  /**
   * @description 修改 activeName
   * @param {*} state
   * @param activeName 当前激活菜单
   */
  changeActiveName(state, activeName) {
    state.activeName = activeName;
  },
};
const actions = {
  /**
   * @description 多模式设置路由
   * @param {*} { commit }
   * @param mode
   * @returns
   */
  async setRoutes({ commit }, mode = "none") {
    // 默认前端路由
    let routes = [...asyncRoutes];
    // 设置游客路由关闭路由拦截(不需要可以删除)
    const control = mode === "visit" ? false : rolesControl;
    // 设置后端路由(不需要可以删除)
    if (authentication === "all") {
      //原始的返回数据结构  list数据结构
      const { data } = await getMenuList({});
      let list = actions.jsonDataMenu(data);
      // console.log('返回的list:')
      if (!isArray(list))
        Vue.prototype.$baseMessage(
          "路由格式返回有误！",
          "error",
          "vab-hey-message-error"
        );
      if (list[list.length - 1].path !== "*")
        list.push({ path: "*", redirect: "/404", meta: { hidden: true } });
      routes = convertRouter(list);
    }
    // 根据权限和rolesControl过滤路由
    const accessRoutes = filterRoutes([...constantRoutes, ...routes], control);
    // 设置菜单所需路由
    commit("setRoutes", JSON.parse(JSON.stringify(accessRoutes)));
    // 根据可访问路由重置Vue Router
    // console.log('根据可访问路由重置Vue Router:')
    // console.log(accessRoutes)
    await resetRouter(accessRoutes);
  },
  /*前端 组合数据 */
  jsonDataMenu(data) {
    // console.log(' jsonDataMenu 进来了：')
    console.log(data);
    // {
    //   path: '/business/list',
    //   name: 'Business',
    //   component: Layout,
    //   meta: {
    //     title: '商户',
    //     icon: 'archive-drawer-line',
    //     breadcrumbHidden: true,
    //   },
    //   children: [
    //     {
    //       path: '/business/list',
    //       name: 'BusinessIndex',
    //       component: () => import('@/views/business/index'),
    //       meta: {
    //         title: '商户列表',
    //         icon: 'file-list-line',
    //       },
    //     },
    //   ],
    // }
    let item = [];
    data.forEach((ele) => {
      let obj = {};
      obj.path = ele.path;
      obj.name = ele.name;
      obj.component = ele.component;
      let meta1 = {};
      meta1.title = ele.title;
      meta1.icon = ele.icon;
      if (ele.breadcrumbHidden) {
        meta1.breadcrumbHidden = ele.breadcrumbHidden;
      }
      obj.meta = meta1;
      let children1 = [];
      let childrenV = ele.children;
      if (childrenV && childrenV.length > 0) {
        childrenV.forEach((k) => {
          let obj2 = {};
          obj2.path = k.path;
          obj2.name = k.name;
          let meta2 = {};
          meta2.title = k.title;
          meta2.icon = k.icon;
          //标签小红点
          if (k.badge) {
            meta2.badge = k.badge;
          }
          //关闭所有的标签页 默认展示的标签页面
          if (k.noClosable) {
            meta2.noClosable = k.noClosable;
          }
          //无边栏展示，建议少用
          if (k.noColumn) {
            meta2.noColumn = k.noColumn;
          }
          //判断是否有详情与列表同一个等级
          if (k.dynamicNewTab) {
            meta2.activeMenu = k.activeMenu;
            meta2.dynamicNewTab = k.dynamicNewTab;
          }
          obj2.meta = meta2;
          //判断是否还有三级页面
          let children2 = [];
          let childrenV2 = k.children;
          if (childrenV2 && childrenV2.length > 0) {
            childrenV2.forEach((row) => {
              let obj3 = {};
              obj3.path = row.path;
              obj3.name = row.name;
              obj3.component = row.component;
              let meta3 = {};
              meta3.title = row.title;
              meta3.icon = row.icon;
              //标签小红点
              if (row.badge) {
                meta3.badge = row.badge;
              }
              //判断是否有详情与列表同一个等级
              if (!row.meta.hidden) {
                //接口数据返回的 hidden true是显示  false是隐藏  数据库0是隐藏=false  1显示=true
                meta3.activeMenu = row.activeMenu;
                meta3.dynamicNewTab = row.dynamicNewTab;
                meta3.hidden = true; //vue路由 true为隐藏
              }
              obj3.meta = meta3;
              children2.push(obj3);
            });
          } else {
            //没有子页面
            obj2.component = k.component;
          }
          obj2.children = children2;
          children1.push(obj2);
        });
      }
      obj.children = children1;
      item.push(obj);
    });
    // console.log('前端组合的数据')
    console.log(item);
    return item;
  },
  /**
   * @description 修改Route Meta
   * @param {*} { commit }
   * @param options
   */
  changeMenuMeta({ commit }, options = {}) {
    commit("changeMenuMeta", options);
  },
  /**
   * @description 修改 activeName
   * @param {*} { commit }
   * @param activeName 当前激活菜单
   */
  changeActiveName({ commit }, activeName) {
    commit("changeActiveName", activeName);
  },
};
export default { state, getters, mutations, actions };
