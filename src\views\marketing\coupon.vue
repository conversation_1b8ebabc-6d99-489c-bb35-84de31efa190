<template>
  <div class="index-container">
    <!-- 查询条件 -->
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form :inline="true" size="mini">
          <el-form-item label="操作">
            <el-button type="primary" @click="handleAddCoupon">新增优惠券</el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
    </vab-query-form>

    <!-- 表格 -->
    <el-table ref="tableSort" v-loading="loading" border :data="tableData">
      <div slot="empty" style="margin: 50px 0">
        <img class="img" src="https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/image/1865665326720536578.png" />
      </div>
      <el-table-column align="center" prop="id" label="优惠券ID" min-width="80" />
      <el-table-column align="center" prop="name" label="优惠券名称" min-width="120" />

      <el-table-column align="center" prop="type" label="优惠券类型" min-width="120">
        <template #default="{ row }">
          {{ row.couponTypeName }}
        </template>
      </el-table-column>

      <el-table-column align="center" prop="condition" label="领取条件" min-width="120">
        <template #default="{ row }">
          {{ row.receiveConditionName }}
        </template>
      </el-table-column>

      <el-table-column align="center" prop="createTime" label="创建时间" min-width="120">
        <template #default="{ row }">
          {{ row.createTime }}
        </template>
      </el-table-column>

      <el-table-column align="center" prop="publishTime" label="发放时间" min-width="120">
        <template #default="{ row }">
          <div v-if="row.published == 0">
            <el-tag type="warning" v-if="row.publishType == 0">未发布</el-tag>
            <div v-else>{{ row.scheduledPublishTime }}&nbsp;&nbsp;<el-tag v-if="row.publishType == 1">定时</el-tag></div>
          </div>
          <div v-else>
            <div v-if="row.publishType == 0">{{ row.publishTime }}</div>
            <div v-else>{{ row.scheduledPublishTime }}&nbsp;&nbsp;<el-tag v-if="row.publishType == 1">定时</el-tag></div>
          </div>
        </template>
      </el-table-column>

      <el-table-column align="center" prop="total" label="发放数量" width="100">
        <template #default="{ row }">
          {{ row.totalQuota }}
        </template>
      </el-table-column>

      <el-table-column align="center" prop="claimed" label="领取数量" width="100">
        <template #default="{ row }">
          {{ row.collectCount }}
        </template>
      </el-table-column>

      <el-table-column align="center" prop="used" label="使用数量" width="100">
        <template #default="{ row }">
          {{ row.usedCount }}
        </template>
      </el-table-column>

      <el-table-column align="center" prop="usageRate" label="使用率" width="100">
        <template #default="{ row }">
          {{ row.useRate }}
        </template>
      </el-table-column>

      <el-table-column align="center" prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag v-if="row.published == '0'" type="warning">未发放</el-tag>
          <el-tag v-if="row.published == '1'" type="success">已发放</el-tag>
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <div class="operate-style">
            <p>
              <el-link type="primary" icon="el-icon-view" @click="handleView(row)">查看</el-link>
            </p>
            <p v-if="row.published == 0">
              <el-link type="warning" icon="el-icon-edit" @click="handleEdit(row)">编辑</el-link>
            </p>
            <p v-if="row.published == 0 && row.publishType == 0">
              <el-link type="success" icon="el-icon-thumb" @click="handleRelease(row)">发放</el-link>
            </p>
            <p v-if="row.published == 0 && row.publishType == 1">
              <el-link type="danger" icon="el-icon-switch-button" @click="handleCancel(row)">取消</el-link>
            </p>
            <p>
              <el-link type="danger" icon="el-icon-delete" @click="handleDelete(row)">删除</el-link>
            </p>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination v-if="queryForm.total > 0" background :current-page="queryForm.currentPage" :layout="layout"
      :page-size="queryForm.pageSize" :total="queryForm.total" @current-change="handleCurrentChange"
      @size-change="handleSizeChange" />

    <!-- 查看优惠券详情弹窗 -->
    <el-dialog title="优惠券详情" :visible.sync="viewDialogVisible" width="50%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="优惠券ID">{{ currentCoupon.id }}</el-descriptions-item>
        <el-descriptions-item label="优惠券名称">{{ currentCoupon.name }}</el-descriptions-item>
        <el-descriptions-item label="优惠券类型">{{ currentCoupon.couponTypeName }}</el-descriptions-item>
        <el-descriptions-item label="最低消费金额">{{ currentCoupon.minConsumption || '无门槛' }}</el-descriptions-item>
        <el-descriptions-item label="优惠金额" v-if="currentCoupon.couponType == 0">{{ currentCoupon.discountValue
        }}</el-descriptions-item>
        <el-descriptions-item label="折扣值" v-if="currentCoupon.couponType == 1">{{
          convertDiscount(currentCoupon.discountValue) }}折</el-descriptions-item>
        <el-descriptions-item label="领取条件">{{ currentCoupon.receiveConditionName }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ currentCoupon.createTime }}</el-descriptions-item>
        <el-descriptions-item label="发放时间">
          <div v-if="currentCoupon.published == 0">
            <el-tag type="warning" v-if="currentCoupon.publishType == 0">未发布</el-tag>
            <div v-else>{{ currentCoupon.scheduledPublishTime }}&nbsp;&nbsp;<el-tag
                v-if="currentCoupon.publishType == 1">定时</el-tag></div>
          </div>
          <div v-else>
            <div v-if="currentCoupon.publishType == 0">{{ currentCoupon.publishTime }}</div>
            <div v-else>{{ currentCoupon.scheduledPublishTime }}&nbsp;&nbsp;<el-tag
                v-if="currentCoupon.publishType == 1">定时</el-tag></div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="有效开始时间">{{ currentCoupon.validStartTime }}</el-descriptions-item>
        <el-descriptions-item label="有效结束时间">{{ currentCoupon.validEndTime }}</el-descriptions-item>
        <el-descriptions-item label="发放数量">{{ currentCoupon.totalQuota }}</el-descriptions-item>
        <el-descriptions-item label="领取数量">{{ currentCoupon.collectCount }}</el-descriptions-item>
        <el-descriptions-item label="使用数量">{{ currentCoupon.usedCount }}</el-descriptions-item>
        <el-descriptions-item label="使用率">{{ currentCoupon.useRate }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag v-if="currentCoupon.published == '0'" type="warning">未发放</el-tag>
          <el-tag v-if="currentCoupon.published == '1'" type="success">已发放</el-tag>
        </el-descriptions-item>
      </el-descriptions>
      <span slot="footer" class="dialog-footer">
        <el-button @click="viewDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 编辑/新增优惠券弹窗 -->
    <el-drawer :title="dialogTitle" :visible.sync="editDialogVisible" direction="rtl" size="50%"
      :before-close="handleDrawerClose">
      <div style="padding: 0 20px;">
        <el-form :model="couponForm" :rules="rules" label-position="top" ref="couponForm">
          <el-form-item label="优惠券名称" prop="name">
            <el-input v-model="couponForm.name" placeholder="请输入优惠券名称" :maxlength="20" />
          </el-form-item>

          <el-form-item label="优惠券类型" prop="couponType">
            <el-select v-model="couponForm.couponType" placeholder="请选择优惠券类型" style="width: 100%">
              <el-option label="金额券" :value="0" />
              <el-option label="折扣券" :value="1" />
            </el-select>
          </el-form-item>

          <el-form-item label="优惠金额" prop="discountValue" v-if="[0].includes(couponForm.couponType)">
            <el-input v-model.number="couponForm.discountValue" placeholder="请输入优惠金额" type="number" />
          </el-form-item>
          <el-form-item label="折扣比例" prop="discountValue" v-if="[1].includes(couponForm.couponType)">
            <el-input v-model.number="couponForm.discountValue" placeholder="例：85（表示85折）" type="number" />
          </el-form-item>

          <el-form-item label="最低消费金额" prop="minConsumption" v-if="[0, 1].includes(couponForm.couponType)">
            <el-input v-model.number="couponForm.minConsumption" placeholder="请输入最低消费金额，例: 100" type="number" />
            <span style="color: #999; font-size: 12px">不填表示无使用门槛</span>
          </el-form-item>

          <el-form-item label="领取条件" prop="receiveCondition">
            <el-select v-model="couponForm.receiveCondition" placeholder="请选择领取条件" style="width: 100%">
              <el-option label="无限制" :value="0" />
              <el-option label="仅限新用户" :value="1" />
            </el-select>
          </el-form-item>

          <el-form-item label="使用范围" prop="useScope">
            <el-select v-model="couponForm.useScope" placeholder="请选择使用设备" style="width: 100%"
              @change="handleUseScopeChange">
              <el-option label="所有设备" :value="0" />
              <el-option label="指定设备" :value="1" />
            </el-select>
            <div v-if="couponForm.useScope === 1" style="margin-top: 10px;">
              <el-button type="text" @click="showDeviceDialog">选择设备</el-button>
              <span v-if="selectedDevices.length > 0" style="margin-left: 10px; color: #666;">
                已选择 {{ selectedDevices.length }} 个设备
              </span>
            </div>
          </el-form-item>

          <el-form-item label="有效期开始" prop="validStartTime">
            <el-date-picker v-model="couponForm.validStartTime" type="datetime" placeholder="选择日期"
              value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%" @change="handleStartTimeChange" />
          </el-form-item>

          <el-form-item label="有效期结束" prop="validEndTime">
            <el-date-picker v-model="couponForm.validEndTime" type="datetime" placeholder="选择日期"
              value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%" :picker-options="endTimeOptions" />
          </el-form-item>
          <el-form-item label="发放用户">
            <el-select v-model="couponForm.releaseType" placeholder="请选择发放用户" style="width: 100%">
              <el-option label="所有用户" :value="0" />
              <el-option label="指定用户" :value="1" />
            </el-select>
            <div v-if="couponForm.releaseType === 1" style="margin-top: 10px;">
              <div class="phone-input-container">
                <el-input v-model="phoneInput" placeholder="请输入手机号，按回车键添加" @keyup.enter.native="addPhone"
                  style="width: 100%">
                  <el-button slot="append" @click="addPhone">添加</el-button>
                </el-input>
              </div>
              <div class="phone-tags" style="margin-top: 10px;">
                <el-tag v-for="phone in phoneList" :key="phone" closable @close="removePhone(phone)"
                  style="margin-right: 5px; margin-bottom: 5px;">
                  {{ phone }}
                </el-tag>
              </div>
              <div v-if="phoneError" style="color: #F56C6C; font-size: 12px; margin-top: 5px;">
                {{ phoneError }}
              </div>
            </div>
          </el-form-item>
          <el-form-item label="创建数量" prop="totalQuota">
            <el-input-number v-model="couponForm.totalQuota" :min="1" :max="10000" placeholder="例: 100" />
          </el-form-item>

          <el-form-item label="用户领取数量限制">
            <el-input type="number" v-model.number="couponForm.perLimit" placeholder="例: 1 (每位用户最多可领取数量)" />
            <span style="color: #999; font-size: 12px">限制每位用户最多可领取的优惠券数量</span>
          </el-form-item>

          <el-form-item style="text-align: center; margin-top: 20px;">
            <el-button @click="editDialogVisible = false" style="width: 120px; margin-right: 20px;">取 消</el-button>
            <el-button type="primary" @click="submitForm" style="width: 120px;">{{ dialogTitle === '新增优惠券' ? '创建优惠券' :
              '更新优惠券' }}</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>

    <!-- 确认发放弹窗 -->
    <el-dialog title="确认发放" :visible.sync="releaseDialogVisible" width="30%">
      <p>确定要发放 "{{ currentCoupon.name }}" 优惠券吗？</p>
      <el-form :model="releaseForm" label-width="100px" style="margin-top: 20px;">
        <el-form-item label="发放方式">
          <el-radio-group v-model="releaseForm.publishType">
            <el-radio :label="1">立即发放</el-radio>
            <el-radio :label="2">定时发放</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="发放时间" v-if="releaseForm.publishType === 2">
          <el-date-picker v-model="releaseForm.publishTime" type="datetime" placeholder="选择发放时间"
            value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%" :picker-options="publishTimeOptions" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="releaseDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmRelease">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 确认取消弹窗 -->
    <el-dialog title="确认取消" :visible.sync="cancelDialogVisible" width="30%">
      <p>确定要取消 "{{ currentCoupon.name }}" 优惠券吗？</p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmCancel">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 设备选择弹窗 -->
    <el-dialog title="选择设备" :visible.sync="deviceDialogVisible" width="60%">
      <el-form :inline="true" :model="deviceQueryForm" class="demo-form-inline">
        <el-form-item label="设备名称">
          <el-input v-model="deviceQueryForm.deviceName" placeholder="请输入设备名称" clearable />
        </el-form-item>
        <el-form-item label="设备编号">
          <el-input v-model="deviceQueryForm.deviceCode" placeholder="请输入设备编号" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleDeviceQuery">查询</el-button>
        </el-form-item>
      </el-form>

      <el-table ref="deviceTable" :data="deviceList" style="width: 100%" @selection-change="handleDeviceSelectionChange"
        v-loading="deviceLoading">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="设备ID" />
        <el-table-column prop="merchantName" label="设备商户" />
        <el-table-column prop="deviceCode" label="设备位置">
          <template #default="{ row }">
            {{ row.provinceName }}{{ row.cityName }}{{ row.districtName }}{{ row.address }}
          </template>
        </el-table-column>
        <el-table-column prop="" label="设备状态">
          <template #default="{ row }">
            <el-tag v-if="row.networkStatus == 'online'" size="mini" type="success">
              在线
            </el-tag>
            <el-tag v-if="row.networkStatus == 'offline'" size="mini" type="warning">
              离线
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container" style="margin-top: 15px; text-align: right;">
        <el-pagination background :current-page="deviceQueryForm.currentPage" :page-sizes="[10, 20, 30, 50]"
          :page-size="deviceQueryForm.pageSize" layout="total, sizes, prev, pager, next, jumper"
          :total="deviceQueryForm.total" @size-change="handleDeviceSizeChange"
          @current-change="handleDevicePageChange" />
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="deviceDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmDeviceSelection">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 确认删除弹窗 -->
    <el-dialog title="确认删除" :visible.sync="deleteDialogVisible" width="30%">
      <p>确定要删除 "{{ currentCoupon.name }}" 裂变券吗？</p>
      <p style="color: #f56c6c; font-size: 12px;">删除后将无法恢复，请谨慎操作！</p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="danger" @click="confirmDelete">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { couponList, createCoupon, getDeviceList, publishCoupons, cancelCoupons, couponDetails, updateCoupon, deleteCoupon } from '@/api/marketing'

export default {
  name: 'CouponManagement',
  components: {},
  data() {
    return {
      loading: false, // 加载状态
      viewDialogVisible: false, // 查看优惠券详情弹窗
      editDialogVisible: false, // 编辑/新增优惠券弹窗
      releaseDialogVisible: false, // 确认发放弹窗
      cancelDialogVisible: false, // 确认取消弹窗
      deleteDialogVisible: false, // 确认删除弹窗
      dialogTitle: '新增优惠券', // 弹窗标题
      currentCoupon: {}, // 当前优惠券
      queryForm: {
        currentPage: 1, // 当前页
        pageSize: 20, // 每页条数
        scene: 1, // 1-普通优惠券，2-裂变券
        total: 0, // 总条数
      },
      layout: 'total, sizes, prev, pager, next, jumper', // 分页布局
      tableData: [], // 表格数据
      couponForm: { // 优惠券表单
        id: null, // 优惠券ID，编辑时使用
        scene: 1, // 场景值：1-普通优惠券，2-裂变券
        name: '', // 优惠券名称
        couponType: null, // 优惠券类型 0:金额券 1:折扣券 2:无门槛券 3:无门槛券
        discountValue: null, // 优惠金额/折扣比例
        validPeriodType: 0, // 有效期类型：0-固定日期，1-领取后N天
        minConsumption: null, // 最低消费金额
        receiveCondition: null, // 领取条件 0:无限制 1:仅限新用户
        useScope: null, // 使用范围：0所有设备，1指定设备
        deviceIds: [], // 指定设备ID列表
        validStartTime: '', // 有效期开始时间
        validEndTime: '', // 有效期结束时间
        totalQuota: 1, // 创建数量
        perLimit: null, // 每人领取数量限制
        releaseType: 0, // 发放用户类型 0:所有用户 1:指定用户
        phoneList: [], // 手机号列表
      },
      rules: { // 表单验证规则
        name: [
          { required: true, message: '请输入优惠券名称', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        couponType: [
          { required: true, message: '请选择优惠券类型', trigger: 'change' }
        ],
        discountValue: [
          { required: true, message: '请输入优惠金额', trigger: 'blur' }
        ],
        minConsumption: [
          { pattern: /^\d+$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ],
        receiveCondition: [
          { required: true, message: '请选择领取条件', trigger: 'change' }
        ],
        useScope: [
          { required: true, message: '请选择使用范围', trigger: 'change' }
        ],
        validStartTime: [
          { required: true, message: '请选择有效期开始时间', trigger: 'change' }
        ],
        validEndTime: [
          { required: true, message: '请选择有效期结束时间', trigger: 'change' }
        ],
        totalQuota: [
          { required: true, message: '请输入创建数量', trigger: 'blur' }
        ],
      },
      endTimeOptions: { // 结束时间选项
        disabledDate: () => false
      },
      deviceDialogVisible: false, // 设备选择弹窗
      deviceList: [], // 设备列表
      selectedDevices: [], // 已选择的设备
      deviceLoading: false, // 设备列表加载状态
      deviceQueryForm: { // 设备查询表单
        currentPage: 1, // 当前页
        pageSize: 10, // 每页条数
        total: 0, // 总条数
        deviceName: '', // 设备名称
        deviceCode: '' // 设备编号
      },
      releaseForm: {
        templateId: null, // 优惠券模板ID
        publishType: 1, // 1: 立即发放, 2: 定时发放
        publishTime: '' // 定时发放时间
      },
      publishTimeOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7; // 不能选择过去的日期
        }
      },
      phoneInput: '', // 手机号输入框的值
      phoneError: '', // 手机号错误提示
    }
  },
  created() {
    this.fetchData()
    // 监听优惠券类型变化，动态修改验证规则
    this.$watch('couponForm.couponType', (newVal) => {
      // 重置表单验证
      if (this.$refs.couponForm) {
        this.$refs.couponForm.clearValidate()
      }

      if (newVal === 1) { // 金额券
        this.rules.minConsumption = [
          { pattern: /^\d+$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ]
        this.rules.discountValue = [
          { required: true, message: '请输入优惠金额', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ]
      } else { // 折扣券
        // 折扣券不需要最低消费金额
        this.rules.minConsumption = []
        // 折扣券需要折扣比例
        this.rules.discountValue = [
          { required: true, message: '请输入折扣比例', trigger: 'blur' }
        ]
      }
    })
  },
  methods: {
    // 获取优惠券列表
    async fetchData() {
      this.loading = true
      try {
        const { code, data } = await couponList(this.queryForm)
        if (code === '00000') {
          this.tableData = data.data
          this.queryForm.total = data.paginator.totalRecord
        }
      } catch (error) {
        console.error('获取优惠券列表失败', error)
      } finally {
        this.loading = false
      }
    },
    /**
     * 将中文折扣格式转换为小数折扣（如65折 → 6.5折）
     * @param {string|number} input - 折扣输入（支持 "65折"、"6.5折"、65 等形式）
     * @returns {string} 标准小数折扣格式（如 "6.5折"）
     * @throws {Error} 输入无效时抛出错误
     */
    convertDiscount(input) {
      // 参数校验
      if (input === null || input === undefined) {
        throw new Error("折扣值不能为空");
      }

      // 统一转为字符串处理
      const strInput = String(input).trim();

      // 正则匹配提取纯数字部分（支持 "65折"、"6.5折"、65 等格式）
      const match = strInput.match(/^(\d+\.?\d*)(折?)$/);
      if (!match) {
        throw new Error(`无效的折扣格式: ${input}`);
      }

      const numberPart = match[1]; // 数字部分（如 "65"、"6.5"）
      const unit = match[2];       // 单位部分（如 "折" 或空）

      // 转换为数字并验证范围
      const numericValue = parseFloat(numberPart);
      if (isNaN(numericValue) || numericValue <= 0 || numericValue >= 100) {
        throw new Error("折扣值需在 0 < 折扣 < 100 范围内");
      }

      // 根据数值范围智能转换
      let result;
      if (numericValue >= 10) {
        // 处理 "65折" → 6.5 折
        result = (numericValue / 10).toFixed(1);
      } else if (numericValue.toString().includes('.')) {
        // 处理已有小数的输入（如 "6.5折" → 无需修改）
        result = numericValue.toFixed(1);
      } else {
        // 处理单数字输入（如 "5折" → 5.0折）
        result = numericValue.toFixed(1);
      }

      // 拼接单位（如果原始输入带单位则保留）
      return `${result}${unit}`;
    },
    // 分页处理
    handleSizeChange(val) {
      this.queryForm.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.queryForm.currentPage = val
      this.fetchData()
    },
    // 删除裂变券
    handleDelete(row) {
      this.currentCoupon = { ...row }
      this.deleteDialogVisible = true
    },
    // 确认删除
    async confirmDelete() {
      try {
        const { code } = await deleteCoupon({ id: Number(this.currentCoupon.id) })
        if (code === '00000') {
          this.$message.success('裂变券删除成功')
          this.fetchData()
        } else {
          this.$message.error('删除裂变券失败')
        }
      } catch (error) {
        console.error('删除裂变券失败', error)
        this.$message.error('删除裂变券失败')
      } finally {
        this.deleteDialogVisible = false
      }
    },
    // 查看优惠券详情
    async handleView(row) {
      try {
        const { code, data } = await couponDetails({ id: Number(row.id) })
        if (code === '00000') {
          this.currentCoupon = data
          this.viewDialogVisible = true
        } else {
          this.$message.error('获取优惠券详情失败')
        }
      } catch (error) {
        console.error('获取优惠券详情失败', error)
        this.$message.error('获取优惠券详情失败')
      }
    },

    // 新增优惠券
    handleAddCoupon() {
      this.dialogTitle = '新增优惠券'
      this.couponForm = {
        scene: 1, // 场景值：1-普通优惠券，2-裂变券
        name: '', // 优惠券名称
        couponType: null, // 优惠券类型 1:金额券 2:折扣券 3:无门槛券
        discountValue: null, // 优惠金额/折扣比例
        validPeriodType: 0, // 有效期类型：0-固定日期，1-领取后N天
        minConsumption: null, // 最低消费金额
        receiveCondition: null, // 领取条件 0:无限制 1:仅限新用户
        useScope: null, // 使用范围：0所有设备，1指定设备
        deviceIds: [], // 指定设备ID列表
        validStartTime: '', // 有效期开始时间
        validEndTime: '', // 有效期结束时间
        totalQuota: 1, // 创建数量
        perLimit: null, // 每人领取数量限制
        releaseType: 0, // 发放用户类型 0:所有用户 1:指定用户
        phoneList: [], // 手机号列表
      }
      this.selectedDevices = []
      this.phoneList = []
      this.phoneInput = ''
      this.phoneError = ''
      this.editDialogVisible = true
    },

    // 编辑优惠券
    async handleEdit(row) {
      this.dialogTitle = '编辑优惠券'
      try {
        const { code, data } = await couponDetails({ id: Number(row.id) })
        if (code === '00000') {
          this.couponForm = {
            scene: data.scene, // 场景值：1-普通优惠券，2-裂变券
            name: data.name, // 优惠券名称
            couponType: data.couponType, // 优惠券类型
            discountValue: data.discountValue, // 优惠金额或折扣比例
            minConsumption: data.minConsumption || 0, // 最低消费金额，默认为0表示无门槛
            receiveCondition: data.receiveCondition, // 领取条件
            useScope: data.useScope, // 使用范围
            deviceIds: data.deviceIds || [], // 设备ID列表，默认为空数组
            validStartTime: data.validStartTime, // 有效期开始时间
            validEndTime: data.validEndTime, // 有效期结束时间
            totalQuota: data.totalQuota, // 创建数量
            perLimit: data.perLimit || 1, // 每人领取限制，默认为1
            validPeriodType: data.validPeriodType || 0, // 有效期类型，默认为固定日期
            releaseType: data.releaseType || 0, // 发放用户类型 0:所有用户 1:指定用户
            phoneList: data.phoneList || [], // 加载已有手机号
          }

          if (data.deviceIds && data.deviceIds.length > 0 && data.deviceList) {
            this.selectedDevices = data.deviceList
          } else {
            this.selectedDevices = []
          }

          this.phoneList = data.phoneList || []
          this.currentCoupon = data
          this.editDialogVisible = true
        } else {
          this.$message.error('获取优惠券详情失败')
        }
      } catch (error) {
        console.error('获取优惠券详情失败', error)
        this.$message.error('获取优惠券详情失败')
      }
    },

    // 发放优惠券
    handleRelease(row) {
      this.currentCoupon = { ...row }
      this.releaseForm.templateId = row.id
      this.releaseDialogVisible = true
    },

    // 取消优惠券
    handleCancel(row) {
      this.currentCoupon = { ...row }
      this.cancelDialogVisible = true
    },

    // 确认发放
    async confirmRelease() {
      if (this.releaseForm.publishType === 2 && !this.releaseForm.publishTime) {
        this.$message.warning("请选择发放时间");
        return
      }
      try {
        const { code } = await publishCoupons(this.releaseForm)
        if (code === '00000') {
          this.$message.success('优惠券发放成功')
          this.fetchData()
        }
      } catch (error) {
        this.$message.error('优惠券发放失败')
      } finally {
        this.releaseDialogVisible = false
        this.releaseForm = {
          publishType: 1,
          publishTime: ''
        }
      }
    },

    // 确认取消
    async confirmCancel() {
      try {
        const { code } = await cancelCoupons({ id: Number(this.currentCoupon.id) })
        if (code === '00000') {
          this.$message.success('优惠券已取消')
          this.fetchData()
        }
      } catch (error) {
        this.$message.error('取消优惠券失败')
      } finally {
        this.cancelDialogVisible = false
      }
    },

    // 提交表单
    submitForm() {
      this.$refs.couponForm.validate(async valid => {
        if (valid) {
          if (this.couponForm.releaseType === 1 && this.phoneList.length === 0) {
            this.$message.warning('请至少添加一个手机号')
            return false
          }
          this.couponForm.discountValue = parseFloat(this.couponForm.discountValue);
          if (this.couponForm.minConsumption !== null && this.couponForm.minConsumption !== '') {
            this.couponForm.minConsumption = parseFloat(this.couponForm.minConsumption);
          } else {
            this.couponForm.minConsumption = 0; // 如果为空，设为0表示无门槛
          }
          this.couponForm.perLimit = parseInt(this.couponForm.perLimit);

          if (this.couponForm.useScope == 1 && this.selectedDevices.length > 0) {
            this.couponForm.deviceIds = this.selectedDevices.map(device => device.id);
          }
          if (this.dialogTitle === '新增优惠券') {
            try {
              const { code } = await createCoupon(this.couponForm)
              if (code === '00000') {
                this.$message.success('创建优惠券成功')
                this.fetchData()
              }
            } catch (error) {
              this.$message.error('创建优惠券失败')
            }
          } else {
            try {
              const { code } = await updateCoupon({
                id: this.currentCoupon.id,
                ...this.couponForm
              })
              if (code === '00000') {
                this.$message.success('编辑优惠券成功')
                this.fetchData()
              }
            } catch (error) {
              this.$message.error('编辑优惠券失败')
            }
          }

          this.editDialogVisible = false
        } else {
          return false
        }
      })
    },

    // 检查是否可以编辑
    canEdit(row) {
      return row.claimed === 0 && row.used === 0
    },

    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')

      return `${year}-${month}-${day} ${hours}:${minutes}`
    },

    // 处理抽屉关闭前的回调
    handleDrawerClose(done) {
      this.$confirm('确认关闭？未保存的数据将会丢失')
        .then(() => {
          done()
        })
        .catch(() => { })
    },

    // 处理开始时间变化
    handleStartTimeChange(val) {
      if (!val) {
        this.endTimeOptions.disabledDate = () => false;
        return;
      }

      this.endTimeOptions.disabledDate = time => {
        if (!time) return false;
        return time.getTime() < new Date(val).getTime();
      };

      if (this.couponForm.validEndTime && new Date(this.couponForm.validEndTime) < new Date(val)) {
        this.couponForm.validEndTime = '';
      }
    },

    // 处理使用范围变化
    handleUseScopeChange(value) {
      if (value === 1) {
        this.fetchDeviceList()
      } else {
        this.selectedDevices = []
        this.couponForm.deviceIds = []
      }
    },

    // 显示设备选择弹窗
    showDeviceDialog() {
      this.deviceDialogVisible = true
    },

    // 获取设备列表数据
    async fetchDeviceList() {
      this.deviceLoading = true
      try {
        const { code, data } = await getDeviceList(this.deviceQueryForm)
        if (code === '00000') {
          this.deviceList = data.data
          this.deviceQueryForm.total = data.paginator.totalRecord

          this.$nextTick(() => {
            if (this.selectedDevices.length > 0 && this.$refs.deviceTable) {
              this.deviceList.forEach(row => {
                if (this.selectedDevices.some(device => device.id == row.id)) {
                  this.$refs.deviceTable.toggleRowSelection(row, true)
                }
              })
            }
          })
          this.deviceDialogVisible = true
        } else {
          this.$message.error('获取设备列表失败')
        }
      } catch (error) {
        console.error('获取设备列表失败', error)
        this.$message.error('获取设备列表失败')
      } finally {
        this.deviceLoading = false
      }
    },

    // 设备查询
    handleDeviceQuery() {
      this.deviceQueryForm.currentPage = 1
      this.fetchDeviceList()
    },

    // 设备分页
    handleDevicePageChange(page) {
      this.deviceQueryForm.currentPage = page
      this.fetchDeviceList()
    },

    // 设备每页条数变化
    handleDeviceSizeChange(size) {
      this.deviceQueryForm.pageSize = size
      this.deviceQueryForm.currentPage = 1
      this.fetchDeviceList()
    },

    // 处理设备选择变化
    handleDeviceSelectionChange(selection) {
      this.selectedDevices = selection
      this.couponForm.deviceIds = selection.map(device => device.id)
    },

    // 确认设备选择
    confirmDeviceSelection() {
      if (this.selectedDevices.length === 0) {
        this.$message.warning('请至少选择一个设备')
        return
      }
      this.deviceDialogVisible = false
    },

    // 添加手机号
    addPhone() {
      const phone = this.phoneInput.trim()
      if (!phone) {
        this.phoneError = '请输入手机号'
        return
      }

      const phoneRegex = /^1[3-9]\d{9}$/
      if (!phoneRegex.test(phone)) {
        this.phoneError = '请输入正确的手机号格式'
        return
      }

      if (this.phoneList.includes(phone)) {
        this.phoneError = '该手机号已添加'
        return
      }

      this.phoneList.push(phone)
      this.phoneInput = ''
      this.phoneError = ''

      this.couponForm.phoneList = this.phoneList
    },

    // 移除手机号
    removePhone(phone) {
      const index = this.phoneList.indexOf(phone)
      if (index > -1) {
        this.phoneList.splice(index, 1)
        this.couponForm.phoneList = this.phoneList
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.index-container {
  padding: 10px;
}

.dialog-footer {
  margin-top: 20px;
}

.el-dialog__body {
  padding: 20px;
}

.el-descriptions {
  margin-bottom: 20px;
}

.phone-input-container {
  width: 100%;
}

.phone-tags {
  margin-top: 10px;
}

.operate-style {
  min-height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  p {
    margin: 2px 0;
  }
}
</style>
