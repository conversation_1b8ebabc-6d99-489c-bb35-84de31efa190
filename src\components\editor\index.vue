<template>
  <quill-editor
    v-model="myContent"
    class="editor"
    :options="editorOption"
    @blur="onEditorBlur($event)"
    @change="onEditorChange($event)"
    @focus="onEditorFocus($event)"
  />
</template>

<script>
  import 'quill/dist/quill.core.css'
  import 'quill/dist/quill.snow.css'
  import { quillEditor, Quill } from 'vue-quill-editor'
  import { ImageExtend, QuillWatch } from 'quill-image-extend-module'
  import imageResize from 'quill-image-resize-module'
  import store from '@/store'
  import { baseURL } from '@/config'
  Quill.register('modules/ImageExtend', ImageExtend)
  Quill.register('modules/imageResize', imageResize)
  // 工具栏配置
  const toolbarOptions = [
    ['bold', 'italic', 'underline', 'strike'],
    ['blockquote', 'code-block'],
    [{ header: 1 }, { header: 2 }],
    [{ list: 'ordered' }, { list: 'bullet' }],
    [{ script: 'sub' }, { script: 'super' }],
    [{ indent: '-1' }, { indent: '+1' }],
    [{ direction: 'rtl' }],
    [{ size: ['small', false, 'large', 'huge'] }],
    [{ header: [1, 2, 3, 4, 5, 6, false] }],
    [{ color: [] }, { background: [] }],
    [{ font: [] }],
    [{ align: [] }],
    ['clean'],
    ['link', 'image'],
  ]
  export default {
    name: 'Quill',
    components: {
      quillEditor,
    },
    props: {
      content: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        // 富文本
        myContent: this.content,
        editorOption: {
          placeholder: '请输入文档内容~',
          theme: 'snow', // or 'bubble'
          modules: {
            ImageExtend: {
              loading: true,
              name: 'file',
              size: 6, // 可选参数 图片大小，单位为M，1M = 1024kb
              headers: (xhr) => {
                xhr.setRequestHeader('token', store.getters['user/token'])
              },
              action: baseURL + '/file/common/image/upload',
              response: (res) => {
                return res.data
              },
            },
            imageResize: {
              displayStyles: {
                backgroundColor: 'black',
                border: 'none',
                color: 'white',
              },
              modules: ['Resize', 'DisplaySize', 'Toolbar'],
            },
            toolbar: {
              container: toolbarOptions,
              handlers: {
                image: function () {
                  QuillWatch.emit(this.quill.id)
                },
              },
            },
          },
        },
      }
    },
    computed: {},
    watch: {
      // 监听content值变化, 确保输入框中的数据刷新
      content() {
        this.myContent = this.content
      },
    },
    beforeMount() {},
    beforeDestroy() {},
    created() {},
    methods: {
      // 失去焦点事件
      onEditorBlur() {
        this.$emit('onEditorBlur', this.myContent)
      },
      // 获得焦点事件
      onEditorFocus() {
        this.$emit('onEditorFocus', this.myContent)
      },
      // 内容改变事件
      onEditorChange() {
        this.$emit('onEditorChange', this.myContent)
      },
    },
  }
</script>
<style>
  .ql-editor {
    min-height: 520px;
  }
  .ql-snow .ql-tooltip[data-mode='link']::before {
    content: '请输入链接地址:' !important;
  }

  .ql-snow .ql-tooltip.ql-editing a.ql-action::after {
    border-right: 0px;
    content: '保存';
    padding-right: 0px;
  }

  .ql-snow .ql-tooltip[data-mode='video']::before {
    content: '请输入视频地址:' !important;
  }

  .ql-snow .ql-picker.ql-size .ql-picker-label::before,
  .ql-snow .ql-picker.ql-size .ql-picker-item::before {
    content: '14px' !important;
  }

  .ql-snow .ql-picker.ql-size .ql-picker-label[data-value='small']::before,
  .ql-snow .ql-picker.ql-size .ql-picker-item[data-value='small']::before {
    content: '10px' !important;
  }

  .ql-snow .ql-picker.ql-size .ql-picker-label[data-value='large']::before,
  .ql-snow .ql-picker.ql-size .ql-picker-item[data-value='large']::before {
    content: '18px' !important;
  }

  .ql-snow .ql-picker.ql-size .ql-picker-label[data-value='huge']::before,
  .ql-snow .ql-picker.ql-size .ql-picker-item[data-value='huge']::before {
    content: '32px' !important;
  }

  .ql-snow .ql-picker.ql-header .ql-picker-label::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item::before {
    content: '文本' !important;
  }

  .ql-snow .ql-picker.ql-header .ql-picker-label[data-value='1']::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='1']::before {
    content: '标题1' !important;
  }

  .ql-snow .ql-picker.ql-header .ql-picker-label[data-value='2']::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='2']::before {
    content: '标题2' !important;
  }

  .ql-snow .ql-picker.ql-header .ql-picker-label[data-value='3']::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='3']::before {
    content: '标题3' !important;
  }

  .ql-snow .ql-picker.ql-header .ql-picker-label[data-value='4']::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='4']::before {
    content: '标题4' !important;
  }

  .ql-snow .ql-picker.ql-header .ql-picker-label[data-value='5']::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='5']::before {
    content: '标题5' !important;
  }

  .ql-snow .ql-picker.ql-header .ql-picker-label[data-value='6']::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='6']::before {
    content: '标题6' !important;
  }

  .ql-snow .ql-picker.ql-header .ql-picker-label::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item::before {
    content: '正常' !important;
  }

  .ql-snow .ql-picker.ql-font .ql-picker-label[data-value='SimSun']::before,
  .ql-snow .ql-picker.ql-font .ql-picker-item[data-value='SimSun']::before {
    content: '宋体' !important;
  }

  .ql-font-SimSun {
    font-family: 'SimSun';
  }

  .ql-snow .ql-picker.ql-font .ql-picker-label[data-value='SimHei']::before,
  .ql-snow .ql-picker.ql-font .ql-picker-item[data-value='SimHei']::before {
    content: '黑体' !important;
  }

  .ql-font-SimHei {
    font-family: 'SimHei' !important;
  }

  .ql-snow
    .ql-picker.ql-font
    .ql-picker-label[data-value='Microsoft-YaHei']::before,
  .ql-snow
    .ql-picker.ql-font
    .ql-picker-item[data-value='Microsoft-YaHei']::before {
    content: '微软雅黑' !important;
  }

  .ql-font-Microsoft-YaHei {
    font-family: 'Microsoft YaHei';
  }

  .ql-snow .ql-picker.ql-font .ql-picker-label[data-value='KaiTi']::before,
  .ql-snow .ql-picker.ql-font .ql-picker-item[data-value='KaiTi']::before {
    content: '楷体' !important;
  }

  .ql-font-KaiTi {
    font-family: 'KaiTi';
  }

  .ql-snow .ql-picker.ql-font .ql-picker-label[data-value='FangSong']::before,
  .ql-snow .ql-picker.ql-font .ql-picker-item[data-value='FangSong']::before {
    content: '仿宋' !important;
  }

  .ql-font-FangSong {
    font-family: 'FangSong';
  }
</style>
