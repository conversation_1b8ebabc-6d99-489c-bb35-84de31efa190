<template>
  <el-drawer :direction="direction" size="1000px" :visible.sync="dialogFormVisible">
    <div style="padding: 0px 30px">
      <div>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>角色对应的菜单权限</span>
          </div>
          <el-tree ref="tree" :data="treeData" default-expand-all highlight-current node-key="id" :props="defaultProps"
            show-checkbox />
        </el-card>
      </div>
      <div style="text-align: center">
        <el-button type="primary" @click="save">保存权限</el-button>
        <el-button @click="close">取 消</el-button>
      </div>
      <div style="height: 100px"></div>
    </div>
  </el-drawer>
</template>

<script>
import { doRoleMenuQuery, doQueryMenus, doRoleMenuSave } from '@/api/system/role'
export default {
  name: 'RoleMenus',
  data() {
    return {
      id: 0, //角色ID
      title: '',
      clickModel: false,
      direction: 'ltr',
      dialogFormVisible: false,
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'title',
      },
      ids: [], //角色当前菜单ID集合
    }
  },
  created() { },
  methods: {
    // 查询角色当前菜单 ID
    async requestQueryMenus() {
      let { data } = await doQueryMenus({ roleId: this.id })
      let ids = []
      if (data.length > 0) {
        data.forEach((item) => {
          ids.push(item.id)
        })
      }
      this.ids = ids
      this.requestRoleMenuQuery()
    },
    // 查询当前所有菜单
    async requestRoleMenuQuery() {
      let { data } = await doRoleMenuQuery()
      // 递归函数，用于处理每一层的子菜单
      let afterData = this.addCheckedProperty(data)
      console.log(afterData)
      if (data.length > 0) {
        const Loading = this.$baseColorfullLoading(1, '加载中...')
        this.treeData = afterData
        let nodes = []
        data.forEach((item) => {
          if (item.checked) {
            nodes.push({ id: item.id, label: item.title })
          }
          if (item.children && item.children.length > 0) {
            item.children.forEach((k) => {
              if (k.checked) {
                nodes.push({ id: k.id, label: k.title })
              }
              if (k.children && k.children.length > 0) {
                k.children.forEach((j) => {
                  if (j.checked) {
                    nodes.push({ id: j.id, label: j.title })
                  }
                })
              }
            })
          }
        })
        setTimeout(() => {
          this.$refs.tree.setCheckedNodes(nodes)
          Loading.close()
        }, 888)
      } else {
        this.treeData = []
      }
    },
    // 递归函数，用于处理每一层的子菜单
    addCheckedProperty(items) {
      let checkedIds = this.ids
      return items.map(item => {
        // 检查当前 item 的 id 是否在 checkedIds 数组中
        if (checkedIds.includes(item.id)) {
          item.checked = true;
        } else {
          item.checked = false;
        }

        // 如果有子菜单，递归处理子菜单
        if (item.children && item.children.length > 0) {
          item.children = this.addCheckedProperty(item.children);
        }

        return item;
      });
    },
    show(row) {
      this.id = row.id
      // 查询角色当前菜单 ID
      this.requestQueryMenus()
      //显示 dialog
      this.dialogFormVisible = true
    },
    close() {
      this.dialogFormVisible = false
    },
    save() {
      //获取设置的node值  Element Tree 树形控件
      let nodes = this.$refs.tree.getCheckedNodes()
      if (nodes.length > 0) {
        let routeIds = []
        nodes.forEach((rows) => {
          routeIds.push(rows.id)
        })
        this.saveMenus(routeIds)
      } else {
        this.$notify({
          title: '温馨提示',
          message: '至少选择一个菜单，进行保存！',
          type: 'warning',
          offset: 180,
        })
      }
    },
    //保存菜单权限
    async saveMenus(menuIds) {
      // console.log(routeIds)
      const { message } = await doRoleMenuSave({
        menuIds, roleId: this.id,
      })
      this.$baseMessage(message || '更新成功', 'success', 'vab-hey-message-success')
      this.close()
    },
  },
}
</script>
<style lang="scss" scoped>
.image-box {
  .image {
    width: 45px;
    height: 45px;
    margin-right: 10px;
    margin-bottom: 10px;
    cursor: pointer;
    border-radius: 100%;
  }

  .active {
    width: 48px;
    height: 48px;
    border: 3px solid #f34d37;
  }
}
</style>
