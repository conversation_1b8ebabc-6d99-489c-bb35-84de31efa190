<template>
  <div class="user-container">
    <!-- 查询条件 -->
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form ref="form" :inline="true" label-width="78px" :model="queryForm" size="mini">
          <el-form-item label="订单日期">
            <el-date-picker v-model="queryForm.payTime" end-placeholder="结束日期" range-separator="至"
              start-placeholder="开始日期" type="daterange" @change="onDateRange" />
          </el-form-item>
          <el-form-item label="设备编号">
            <el-input v-model="queryForm.deviceNumber" :clearable="true" placeholder="请输入设备编号" />
          </el-form-item>
          <el-form-item label="订单编号">
            <el-input v-model="queryForm.orderNumber" :clearable="true" placeholder="请输入订单编号" />
          </el-form-item>
          <el-form-item label="结算状态">
            <el-select v-model="queryForm.isSettled" placeholder="请选择结算状态" @change="onSettleStatus">
              <el-option label="全部" value="" />
              <el-option label="未结算" value="0" />
              <el-option label="已结算" value="1" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="handleQuery">
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
    </vab-query-form>
    <!-- 查询条件 -->
    <!-- begin表格 -->
    <el-table ref="tableSort" v-loading="loading" border :data="tableData">
      <div slot="empty" style="margin: 50px 0">
        <img class="img" src="https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/image/1865665326720536578.png" />
      </div>
      <el-table-column align="center" label="订单编号" min-width="160">
        <template #default="{ row }">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="设备编号" min-width="130">
        <template #default="{ row }">
          <span>{{ row.deviceId }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="餐品" min-width="130">
        <template #default="{ row }">
          <p>{{ row.dishName }}</p>
        </template>
      </el-table-column>
      <el-table-column align="center" label="价格" width="130">
        <template #default="{ row }">
          <p>￥{{ row.totalPayment }}</p>
        </template>
      </el-table-column>
      <el-table-column align="center" label="数量" prop="quantity" width="100" />
      <el-table-column align="center" label="订单信息" min-width="160">
        <template #default="{ row }">
          <p>口味：{{ row.flavor }}</p>
          <p>打包：{{ row.packaging == 1 ? '是' : '否' }}</p>
          <p>金额：{{ row.totalPayment }}</p>
        </template>
      </el-table-column>
      <el-table-column align="center" label="实际支付" width="130">
        <template #default="{ row }">
          <p v-if="row.actualPayment">￥{{ row.actualPayment }}</p>
          <p v-else>￥0</p>
        </template>
      </el-table-column>
      <el-table-column align="center" label="订单状态" width="130">
        <template #default="{ row }">
          <el-tag v-if="row.orderStatus == 0" type="primary">
            待支付
          </el-tag>
          <el-tag v-if="row.orderStatus == 1" type="success">
            已支付
          </el-tag>
          <el-tag v-if="row.orderStatus == 2" type="success">
            已分账
          </el-tag>
          <el-tag v-if="row.orderStatus == 3" type="warning">
            分账失败
          </el-tag>
          <el-tag v-if="row.orderStatus == 4" type="danger">
            退款中
          </el-tag>
          <el-tag v-if="row.orderStatus == 5" type="success">
            已退款
          </el-tag>
          <el-tag v-if="row.orderStatus == 6" type="warning">
            退款失败
          </el-tag>
          <el-tag v-if="row.orderStatus == 7" type="info">
            已取消
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="制餐状态" width="130">
        <template #default="{ row }">
          <el-tag v-if="row.cookingStatus == 0" type="primary">
            已支付,等待制餐
          </el-tag>
          <el-tag v-if="row.cookingStatus == 1" type="warning">
            制餐中
          </el-tag>
          <el-tag v-if="row.cookingStatus == 2" type="success">
            制餐完成
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="支付时间" width="160">
        <template #default="{ row }">
          <p>{{ timestampToDate(row.createTime) }}</p>
          <!-- <p v-if="row.isSettled">结算时间：{{ timestampToDate(row.settlementTime) }}</p> -->
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="150">
        <template #default="{ row }">
          <div>
            <el-link type="primary" @click="handleOperation(row, 1)">查看详情</el-link>
          </div>
          <div v-if="[1, 2, 3].includes(row.orderStatus)">
            <el-link type="warning" @click="handleOperation(row, 2)">退款</el-link>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-if="queryForm.total > 20" background :current-page="queryForm.currentPage" :layout="layout"
      :page-size="queryForm.pageSize" :total="queryForm.total" @current-change="handleCurrentChange"
      @size-change="handleSizeChange" />
    <!-- end表格 -->
    <!-- 详情 -->
    <el-dialog :center="true" :destroy-on-close="true" title="订单详情" :visible.sync="orderDetailsVisible" width="70%">
      <div v-loading="orderDetailsLoading" class="form-style">
        <div class="form-title">基础信息</div>
        <div class="form-content">
          <div class="form-item">
            <el-form ref="orderDetailsform" :label-position="'left'" label-width="90px">
              <el-form-item label="订单编号">
                <div class="input-refundForm">{{ orderInfo.id }}</div>
              </el-form-item>
              <el-form-item label="设备编号">
                <div class="input-refundForm">{{ orderInfo.deviceId }}</div>
              </el-form-item>
              <el-form-item label="餐品">
                <div class="input-refundForm">{{ orderInfo.dishName }}</div>
              </el-form-item>
              <el-form-item label="数量">
                <div class="input-refundForm">{{ orderInfo.quantity }}</div>
              </el-form-item>
              <el-form-item label="订单价格">
                <div class="input-refundForm">
                  ¥{{ orderInfo.totalPayment }}
                </div>
              </el-form-item>
              <el-form-item label="优惠金额">
                <div class="input-refundForm">
                  ￥{{ orderInfo.couponAmount || 0 }}
                </div>
              </el-form-item>
              <el-form-item label="实际支付">
                <div class="input-refundForm">
                  ¥{{ orderInfo.actualPayment }}&nbsp;&nbsp;&nbsp;&nbsp;
                  <el-button v-if="[1, 2, 3].includes(orderInfo.orderStatus)"
                    @click="handleRefund(orderInfo)">退款</el-button>
                </div>
              </el-form-item>
              <el-form-item label="支付方式">
                <div class="input-refundForm">微信支付</div>
              </el-form-item>
            </el-form>
          </div>
          <div class="form-item">
            <el-form ref="orderDetailsform" :label-position="'left'" label-width="130px">
              <el-form-item label="是否使用优惠券">
                <div class="input-refundForm">
                  {{ orderInfo.couponAmount ? `-${orderInfo.couponAmount}` : '否' }}
                </div>
              </el-form-item>
              <el-form-item label="口味">
                <div class="input-refundForm">
                  {{ orderInfo.flavor }}
                </div>
              </el-form-item>
              <el-form-item label="是否打包">
                <div class="input-refundForm">
                  {{ orderInfo.packaging == 1 ? '是' : '否' }}
                </div>
              </el-form-item>
              <el-form-item label="创建时间">
                <div class="input-refundForm">
                  {{ timestampToDate(orderInfo.createTime) }}
                </div>
              </el-form-item>
              <el-form-item label="支付时间">
                <div class="input-refundForm">
                  {{ timestampToDate(orderInfo.paymentTime) }}
                </div>
              </el-form-item>
              <!-- <el-form-item label="支付单号">
                <div class="input-refundForm">
                  {{ orderInfo.paymentNumber }}
                </div>
              </el-form-item> -->
              <el-form-item label="订单状态">
                <div class="input-refundForm">
                  <el-tag v-if="orderInfo.orderStatus == 0" type="primary">
                    待支付
                  </el-tag>
                  <el-tag v-if="orderInfo.orderStatus == 1" type="success">
                    已支付
                  </el-tag>
                  <el-tag v-if="orderInfo.orderStatus == 2" type="success">
                    已分账
                  </el-tag>
                  <el-tag v-if="orderInfo.orderStatus == 3" type="warning">
                    分账失败
                  </el-tag>
                  <el-tag v-if="orderInfo.orderStatus == 4" type="danger">
                    退款中
                  </el-tag>
                  <el-tag v-if="orderInfo.orderStatus == 5" type="success">
                    已退款
                  </el-tag>
                  <el-tag v-if="orderInfo.orderStatus == 6" type="warning">
                    退款失败
                  </el-tag>
                  <el-tag v-if="orderInfo.orderStatus == 7" type="info">
                    已取消
                  </el-tag>
                </div>
              </el-form-item>
              <el-form-item label="退款原因">
                <div class="input-refundForm">
                  <span>{{
                    orderInfo.refundReason ? orderInfo.refundReason : "无"
                  }}</span>
                </div>
              </el-form-item>
              <el-form-item label="制餐状态">
                <div class="input-refundForm">
                  <el-tag v-if="orderInfo.cookingStatus == 0" type="primary">
                    已支付,等待制餐
                  </el-tag>
                  <el-tag v-if="orderInfo.cookingStatus == 1" type="warning">
                    制餐中
                  </el-tag>
                  <el-tag v-if="orderInfo.cookingStatus == 2" type="success">
                    制餐完成
                  </el-tag>
                  <span v-else>暂无状态</span>
                </div>
              </el-form-item>
              <!-- <el-form-item label="是否结算">
                <el-tag v-if="orderInfo.isSettled" type="success">
                  已结算
                </el-tag>
                <el-tag v-else type="warning">未结算</el-tag>
              </el-form-item> -->
              <!-- <el-form-item label="结算时间">
                <div class="input-refundForm">
                  {{ timestampToDate(orderInfo.withdrawTime) }}
                </div>
              </el-form-item> -->
            </el-form>
          </div>
        </div>
        <div class="form-title">用户信息</div>
        <div class="form-content">
          <div class="form-item">
            <el-form ref="orderDetailsform" :inline="true" :label-position="'left'" label-width="80px">
              <!-- <el-form-item label="微信头像">
                <el-avatar :src="orderInfo.logo" />
              </el-form-item>
              <el-form-item label="微信昵称">
                <div class="input-refundForm">
                  {{ orderInfo.nickName | '用户' }}
                </div>
              </el-form-item> -->
              <el-form-item label="手机号">
                <div class="input-refundForm">{{ orderInfo.phoneNumber }}</div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="orderDetailsVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { orderList, orderDetails, doRefund } from '@/api/order'
export default {
  name: 'OrderIndex',
  data() {
    return {
      orderDetailsVisible: false, // 弹窗状态
      orderDetailsLoading: false, // 加载状态
      payTime: '', // 支付时间
      queryForm: {
        currentPage: 1, // 页码
        pageSize: 20, // 页数
        total: 0, // 总页数
        operType: 0, // 0查询，1 导出
        payStartTime: '', // 支付开始时间
        payEndTime: '', // 支付结束时间
        deviceNumber: '', // 设备编号
        orderNumber: '', // 订单编号
        isSettled: '', // 结算状态：0未结算，1已结算
      },
      loading: false, // 加载状态
      tableData: [], // 表格数据
      layout: 'total, sizes, prev, pager, next, jumper', // 分页布局
      orderInfo: {}, // 订单详情
    }
  },
  computed: {},
  beforeMount() { },
  beforeDestroy() { },
  created() {
    this.$nextTick(() => {
      // 获取列表
      this.fetchData()
    })
  },
  methods: {
    // 时间戳到日期
    timestampToDate(timestamp) {
      if (timestamp) {
        const date = new Date(timestamp)
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        const seconds = String(date.getSeconds()).padStart(2, '0')
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      } else {
        return '-'
      }
    },

    // 选择日期
    onDateRange(e) {
      this.queryForm.payStartTime = this.formatDateToString(e[0])
      this.queryForm.payEndTime = this.formatDateToString(e[1])
      // 查询
      this.handleQuery()
    },
    // 选择结算状态
    onSettleStatus() {
      // 查询
      this.handleQuery()
    },
    // 日期转换成字符串
    formatDateToString(dateString) {
      // 创建一个新的 Date 对象，传入你的日期字符串
      const date = new Date(dateString)
      // 检查是否创建了有效的日期对象
      if (isNaN(date.getTime())) {
        throw new Error('Invalid date string')
      }
      // 提取年份、月份（注意：getMonth() 返回的月份是从0开始的，所以需要加1）、日期
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0') // 确保两位数显示
      const day = String(date.getDate()).padStart(2, '0') // 确保两位数显示

      // 拼接成你想要的格式并返回
      return `${year}-${month}-${day}`
    },
    //查询
    handleQuery() {
      this.tableData = []
      this.queryForm.currentPage = 1
      this.fetchData()
    },
    //分页
    handleSizeChange(val) {
      this.queryForm.currentPage = val
      this.fetchData()
    },
    //分页
    handleCurrentChange(val) {
      this.queryForm.currentPage = val
      this.fetchData()
    },
    // 日期范围
    dateRange(e) {
      if (e) {
        this.queryForm.startDate = e[0] // 起始日期：yyyy-MM-dd
        this.queryForm.stopDate = e[1] // 截止日期：yyyy-MM-dd
      } else {
        this.queryForm.startDate = '' // 起始日期：yyyy-MM-dd
        this.queryForm.stopDate = '' // 截止日期：yyyy-MM-dd
      }
    },
    // 列表查询 查询
    async fetchData() {
      this.loading = true
      const { code, data } = await orderList(this.queryForm)
      if (code == '00000') {
        this.tableData = data.data
        this.queryForm.total = data.paginator.totalRecord
        this.loading = false
      }
    },
    // 更新列表查询
    async updateFetchData() {
      this.loading = true
      const { code, data } = await orderList(this.queryForm)
      if (code == '00000') {
        this.tableData = data.data
        this.queryForm.total = data.paginator.totalRecord
        this.loading = false
      }
    },
    // 订单详情
    async viewOrderDetails(id) {
      const { code, data } = await orderDetails(id)
      if (code == '00000') {
        this.orderInfo = data
      }
      this.orderDetailsLoading = false
    },
    // 退款
    handleRefund(row) {
      let that = this
      this.$prompt(
        `此操作将退款订单号为：${row.id}的订单, 是否继续?`,
        '提示',
        {
          confirmButtonText: '确定退款',
          cancelButtonText: '取消',
          inputPlaceholder: '请输入退款原因',
          inputPattern: /\S/,
          inputErrorMessage: '退款原因不能为空',
          type: 'warning',
        }
      )
        .then(async ({ value }) => {
          let { code, data } = await doRefund({
            outTradeNo: row.id,
            remark: value
          })
          console.log(code, data)
          if (code == '00000') {
            that.$message({
              type: 'success',
              message: '操作成功!',
            })
            that.orderDetailsVisible = false
            that.updateFetchData()
          }
        })
        .catch(() => { })
    },
    // 操作
    handleOperation(row, type) {
      if (type == 1) {
        this.orderDetailsVisible = true
        this.orderDetailsLoading = true
        this.viewOrderDetails({ id: row.id })
      } else if (type == 2) {
        this.handleRefund(row)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.link-padding {
  margin-left: 15px;
}

.form-style {
  display: flex;
  flex-direction: column;

  .form-title {
    font-size: 14px;
    padding-bottom: 14px;
    color: #909399;
    border-bottom: 1px solid #dcdfe6;
    margin: 20px 0;
  }

  .form-content {
    display: flex;
    margin-top: 10px;
    padding: 0 5px;

    .form-item {
      width: 50%;
    }
  }
}

.input-tips {
  font-size: 13px;
  color: #f56c6c;
}

.input-refundForm {
  font-size: 14px;
  color: #409eff;
}

.order-details {
  width: 80%;
  margin: 20px auto;
}

p {
  margin-bottom: 0;
  margin-top: 5px;
}
</style>
