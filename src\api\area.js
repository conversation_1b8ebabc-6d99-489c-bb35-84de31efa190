import request from '@/utils/request'

/**
 * 全国省市区数据
 * @param {*} data
 * @returns
 */
export async function areaList(data) {
  return request({
    url: '/area/tree',
    method: 'post',
    data,
  })
}

/**
 * 获取国家列表
 * @param {Object} data - 请求数据
 * @returns {Promise} 返回国家列表的请求结果
 */
export async function countryList(data) {
  return request({
    url: '/country/pageList',
    method: 'post',
    data,
  })
}

// 查询国家列表
export async function queryByName(data) {
  return request({
    url: '/country/queryByName',
    method: 'post',
    data,
  })
}

/**
 * 获取银行列表
 * @param {Object} data - 请求数据
 * @returns {Promise} 返回银行列表的请求结果
 */
export async function bankList(data) {
  return request({
    url: '/bank/pageList',
    method: 'post',
    data,
  })
}

/**
 * 根据银行名称模糊查询
 * @param {Object} data - 请求数据
 * @returns {Promise} 返回银行列表的请求结果
 */
export async function queryByBankName(data) {
  return request({
    url: '/bank/queryByBankName',
    method: 'post',
    data,
  })
}

/**
 * 获取银行分行列表
 * @param {Object} data - 请求数据
 * @returns {Promise} 返回银行分行列表的请求结果
 */
export async function bankBranchList(data) {
  return request({
    url: '/bankBranch/queryByBankName',
    method: 'post',
    data,
  })
}
