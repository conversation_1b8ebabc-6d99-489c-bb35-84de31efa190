<template>
  <el-drawer :direction="direction" size="1000px" :title="title" :visible.sync="dialogFormVisible">
    <div style="padding: 0px 60px">
      <el-form ref="form" label-width="100px" :model="form" :rules="rules">
        <el-form-item label="父级ID" prop="pid">
          <el-input v-model="form.pid" placeholder="请输入父级ID" />
        </el-form-item>
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入角色编码" />
          <div>示例： ["ROLE_ADMIN"] 非专业人员勿配置</div>
        </el-form-item>
        <el-form-item label="角色描述" prop="info">
          <el-input v-model="form.info" placeholder="请输入角色描述" :rows="2" type="textarea" />
        </el-form-item>
      </el-form>
      <div style="text-align: center">
        <el-button type="primary" @click="save">确 定</el-button>
        <el-button @click="close">取 消</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { doRoleAdd, doRoleUpdate } from '@/api/system/role'
export default {
  name: 'TableEdit',
  data() {
    return {
      type: 1, // 1添加，2编辑
      form: {
        pid: '', // 父级ID
        name: '', // 角色名称
        code: '', // 角色编码
        info: '', // 角色描述
      },
      rules: {
        name: [
          { required: true, trigger: 'blur', message: '请输入角色名称' },
        ],
        code: [
          { required: true, trigger: 'blur', message: '请输入角色编码' },
        ],
      },
      title: '',
      clickModel: false,
      direction: 'ltr',
      dialogFormVisible: false,
    }
  },
  created() { },
  methods: {
    showEdit(row) {
      if (!row) {
        this.title = '添加角色'
        this.type = 1
      } else {
        this.title = '编辑角色'
        this.type = 2
        this.form = Object.assign({}, row)
        // this.form
      }
      //显示 dialog
      this.dialogFormVisible = true
    },
    close() {
      this.$refs['form'].resetFields()
      this.dialogFormVisible = false
    },
    save() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          if (this.type == 1) {
            const { msg } = await doRoleAdd(this.form)
            this.$baseMessage(msg || '添加成功', 'success', 'vab-hey-message-success')
          } else {
            const { msg } = await doRoleUpdate(this.form)
            this.$baseMessage(msg || '修改成功', 'success', 'vab-hey-message-success')
          }
          this.$emit('fetch-data')
          this.close()
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.image-box {
  .image {
    width: 45px;
    height: 45px;
    margin-right: 10px;
    margin-bottom: 10px;
    cursor: pointer;
    border-radius: 100%;
  }

  .active {
    width: 48px;
    height: 48px;
    border: 3px solid #f34d37;
  }
}
</style>
