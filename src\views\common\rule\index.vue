<template>
  <div class="system-role-container">
    <vab-query-form>
      <vab-query-form-left-panel :span="24" />
    </vab-query-form>
    <!-- 角色表格 -->
    <el-table ref="tableSort" v-loading="listLoading" border :data="tableData">
      <el-table-column align="center" label="序号" prop="id" width="100">
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="类型" prop="typeName" width="160" />
      <el-table-column align="center" label="内容" min-width="800">
        <template #default="{ row }">
          <div class="content">{{ row.value }}</div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="160">
        <template #default="{ row }">
          <p>
            <el-button type="primary" plain icon="el-icon-view" size="" @click="handleView(row)">查看</el-button>
          </p>
          <p>
            <el-button type="warning" plain icon="el-icon-edit" @click="handleEdit(row)">编辑</el-button>
          </p>
        </template>
      </el-table-column>
    </el-table>

    <!-- 编辑弹窗 -->
    <el-dialog title="编辑规则" :visible.sync="dialogVisible" width="60%">
      <el-form :model="editForm" label-width="80px">
        <el-form-item label="类型">
          <span>{{ editForm.typeName }}</span>
        </el-form-item>
        <el-form-item label="内容">
          <div style="max-width: 100%; min-height: 520px">
            <quill-editor
              :content="editForm.value"
              @onEditorChange="onEditorChangeServiceContent"
            />
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSave">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 查看弹窗 -->
    <el-dialog title="查看规则" :visible.sync="viewDialogVisible" width="60%">
      <div class="view-container">
        <div class="rule-title">{{ viewForm.typeName }}</div>
        <div class="rule-content" v-html="viewForm.value"></div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="viewDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { ruleList, updateRule } from '@/api/rule'
import QuillEditor from '@/components/editor/index'
export default {
  name: 'Role',
  components: {
    QuillEditor
  },
  filters: {
    statusFilter(status) {
      const statusMap = {
        0: '正常',
        1: '禁用',
      }
      return statusMap[status]
    },
  },
  data() {
    return {
      tableData: [],
      ruleData: {},
      listLoading: true,
      dialogVisible: false,
      editForm: {
        type: '',
        typeName: '',
        value: ''
      },
      viewDialogVisible: false,
      viewForm: {
        type: '',
        typeName: '',
        value: ''
      }
    }
  },
  computed: {},
  beforeMount() { },
  beforeDestroy() { },
  created() {
    this.fetchData()
  },
  methods: {
    //列表
    async fetchData() {
      try {
        this.listLoading = true
        const { data } = await ruleList({})
        this.ruleData = data
        this.tableData = []
        this.tableData.push({ type: 1, typeName: '用户协议', value: data.userAgreement })
        this.tableData.push({ type: 2, typeName: '隐私协议', value: data.privacyPolicy })
        this.tableData.push({ type: 3, typeName: '活动规则', value: data.activityRule })
        this.tableData.push({ type: 4, typeName: '使用规则', value: data.usageRule })
        this.listLoading = false
      } catch {
        this.listLoading = false
      }
    },
    // 监听富文本框
    onEditorChangeServiceContent(content) {
      if (this.editForm.type == 1) {
        this.ruleData.userAgreement = content
      } else if (this.editForm.type == 2) {
        this.ruleData.privacyPolicy = content
      } else if (this.editForm.type == 3) {
        this.ruleData.activityRule = content
      } else if (this.editForm.type == 4) {
        this.ruleData.usageRule = content
      }
    },
    // 查看协议
    handleView(row) {
      this.viewForm = {
        type: row.type,
        typeName: row.typeName,
        value: row.value
      }
      this.viewDialogVisible = true
    },
    // 编辑协议
    handleEdit(row) {
      this.editForm = {
        type: row.type,
        typeName: row.typeName,
        value: row.value
      }
      this.dialogVisible = true
    },
    // 保存编辑内容
    async handleSave() {
      try {
        const { code } = await updateRule(this.ruleData)
        if (code == '00000') {
          this.$message.success('保存成功')
        }
      } catch {
        this.$message.error('保存失败')
      } finally {
        this.dialogVisible = false
        this.fetchData()
      }
    }
  },
}
</script>

<style lang="scss" scoped>
  .content {
    min-width: 780px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  .view-container {
    padding: 20px;

    .rule-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 20px;
      text-align: center;
    }

    .rule-content {
      min-height: 300px;
      line-height: 1.6;

       p {
        margin-bottom: 10px;
      }

      img {
        max-width: 100%;
      }

      table {
        border-collapse: collapse;
        width: 100%;

        td, th {
          border: 1px solid #ddd;
          padding: 8px;
        }
      }
    }
  }
</style>
