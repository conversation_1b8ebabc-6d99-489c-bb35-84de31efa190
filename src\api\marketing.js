import request from "@/utils/request";

// 分页查询优惠券模板
export function couponList(data) {
  return request({
    url: "/coupon/template/page",
    method: "post",
    data,
  });
}

// 创建优惠券模板
export function createCoupon(data) {
  return request({
    url: "/coupon/template/create",
    method: "post",
    data,
  });
}

// 更新优惠券模板
export function updateCoupon(data) {
  return request({
    url: "/coupon/template/update",
    method: "post",
    data,
  });
}

// 优惠券详情
export function couponDetails(data) {
  return request({
    url: "/coupon/template/detail",
    method: "post",
    data,
  });
}

// 获取设备列表
export function getDeviceList(data) {
  return request({
    url: "/device/pageList",
    method: "post",
    data,
  });
}

// 发布优惠券
export function publishCoupons(data) {
  return request({
    url: "/coupon/template/publish",
    method: "post",
    data,
  });
}

// 取消优惠券
export function cancelCoupons(data) {
  return request({
    url: "/coupon/template/cancel",
    method: "post",
    data,
  });
}

// 获取裂变券列表（下拉列表使用）
export function getCouponList(data) {
  return request({
    url: "/coupon/template/page",
    method: "post",
    data,
  });
}

// 分页查询促销活动
export function getActivityList(data) {
  return request({
    url: "/admin/activity/page",
    method: "post",
    data,
  });
}

// 获取促销活动详情
export function getActivityDetail(data) {
  return request({
    url: "/admin/activity/detail",
    method: "post",
    data,
  });
}

// 创建促销活动
export function createActivity(data) {
  return request({
    url: "/admin/activity/create",
    method: "post",
    data,
  });
}

// 更新促销活动
export function updateActivity(data) {
  return request({
    url: "/admin/activity/update",
    method: "post",
    data,
  });
}

// 删除促销活动
export function deleteActivity(data) {
  return request({
    url: "/admin/activity/delete",
    method: "post",
    data,
  });
}
// 删除优惠券
export async function deleteCoupon(data) {
  return request({
    url: "/coupon/template/delete",
    method: "post",
    data,
  });
}
