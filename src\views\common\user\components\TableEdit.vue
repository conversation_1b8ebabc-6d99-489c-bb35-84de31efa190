<template>
  <el-drawer :direction="direction" size="1000px" :title="title" :visible.sync="dialogFormVisible">
    <div style="padding: 0px 60px">
      <el-form ref="form" label-width="100px" :model="form" :rules="rules">
        <el-form-item label="选择商户" prop="merchantId">
          <el-select v-model="form.merchantId" filterable remote :remote-method="remoteMethod" placeholder="请输入搜索商户"
            style="width: 100%">
            <el-option v-for="item in merchantData" :key="item.id" :label="item.merchantName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="登录账号" prop="userName">
          <el-input v-model="form.userName" />
        </el-form-item>
        <el-form-item v-if="type == 1" label="登录密码" prop="pwd">
          <el-input v-model="form.pwd" />
        </el-form-item>
        <el-form-item label="所属角色">
          <el-select v-model="form.roleId" style="width: 100%" placeholder="请选择账户所属角色">
            <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="form.realName" />
        </el-form-item>
        <el-form-item label="头像" required>
          <div class="image-box">
            <img v-for="(item, index) in imageItem" :key="index" class="image" :class="active == index ? 'active' : ''"
              :src="item" @click="selectAvatar(item, index)" />
          </div>
        </el-form-item>
        <el-form-item label="性别" required>
          <el-radio-group v-model="form.gender">
            <el-radio :label="1">男</el-radio>
            <el-radio :label="2">女</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="手机号码" prop="mobile">
          <el-input v-model="form.mobile" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" />
        </el-form-item>
      </el-form>
      <div style="text-align: center">
        <el-button type="primary" @click="save">确 定</el-button>
        <el-button @click="close">取 消</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { doAccountAdd, doAccountUpdate, merchantList } from '@/api/system/user'
import { doRoleList } from '@/api/system/role'
export default {
  name: 'TableEdit',
  data() {
    return {
      type: 1, //1添加  2编辑
      merchantData: [], // 商户列表
      form: {
        merchantId: '', // 商户 ID
        gender: 1,
        mobile: '',
        pwd: '',
        realName: '',
        roleId: '',
        userName: '',
        email: '',
        logo: 'http://webapp-food.oss-cn-shanghai.aliyuncs.com/admin/account/1.jpg',
      },
      rules: {
        merchantId: [
          { required: true, message: '请选择活动区域', trigger: 'change' }
        ],
        userName: [
          { required: true, trigger: 'blur', message: '请输入登录账号' },
        ],
        pwd: [{ required: true, trigger: 'blur', message: '请输入登录密码' }],
        realName: [
          { required: true, trigger: 'blur', message: '请输入真实姓名' },
        ],
        mobile: [
          { required: true, trigger: 'blur', message: '请输入手机号码' },
        ],
        email: [{ required: true, trigger: 'blur', message: '请输入邮箱' }],
      },
      title: '',
      clickModel: false,
      direction: 'ltr',
      dialogFormVisible: false,
      imageItem: [
        'http://webapp-food.oss-cn-shanghai.aliyuncs.com/admin/account/1.jpg',
        'http://webapp-food.oss-cn-shanghai.aliyuncs.com/admin/account/1_1.jpg',
        'http://webapp-food.oss-cn-shanghai.aliyuncs.com/admin/account/3.jpg',
        'http://webapp-food.oss-cn-shanghai.aliyuncs.com/admin/account/4.jpg',
        'http://webapp-food.oss-cn-shanghai.aliyuncs.com/admin/account/5.jpg',
        'http://webapp-food.oss-cn-shanghai.aliyuncs.com/admin/account/6.jpg',
        'http://webapp-food.oss-cn-shanghai.aliyuncs.com/admin/account/7.jpg',
        'http://webapp-food.oss-cn-shanghai.aliyuncs.com/admin/account/8.jpg',
        'http://webapp-food.oss-cn-shanghai.aliyuncs.com/admin/account/9.jpg',
        'http://webapp-food.oss-cn-shanghai.aliyuncs.com/admin/account/10.jpg',
        'http://webapp-food.oss-cn-shanghai.aliyuncs.com/admin/account/11.jpg',
        'http://webapp-food.oss-cn-shanghai.aliyuncs.com/admin/account/12.jpg',
        'http://webapp-food.oss-cn-shanghai.aliyuncs.com/admin/account/13.jpg',
        'http://webapp-food.oss-cn-shanghai.aliyuncs.com/admin/account/14.jpg',
        'http://webapp-food.oss-cn-shanghai.aliyuncs.com/admin/account/15.jpg',
        'http://webapp-food.oss-cn-shanghai.aliyuncs.com/admin/account/16.jpg',
      ],
      active: 0, // 头像下标
      options: [], // 角色
    }
  },
  created() {
    // 获取角色列表
    this.requestRoleList()
    // 获取店铺列表
    this.getStoreList()
  },
  methods: {
    // 搜索商户列表
    remoteMethod(query) {
      console.log(query)
    },
    // 获取角色列表
    async requestRoleList() {
      let { data } = await doRoleList({ "currentPage": 1, "pageSize": 99, "operType": 0 })
      this.options = data.data
    },

    // 获取商户列表
    async getStoreList() {
      let { code, data } = await merchantList({
        currentPage: 1, // 分页参数：页码
        pageSize: 99, // 分页参数：每页条数
        operType: 0
      })
      if (code == '00000') {
        this.merchantData = data.data
      }
    },
    //选择的头像图片
    selectAvatar(event, index) {
      // console.log(event)
      this.form.logo = event
      this.active = index
    },
    async showEdit(row) {
      if (!row) {
        this.title = '添加系统账号'
        this.type = 1
        this.dialogFormVisible = true
      } else {
        this.title = '编辑系统账号'
        this.type = 2
        let obj = Object.assign({}, row)
        this.form = obj
        //显示 dialog
        this.dialogFormVisible = true
        let { code, data } = await merchantList({
          currentPage: 1, // 分页参数：页码
          pageSize: 99, // 分页参数：每页条数
          operType: 0
        })
        if (code == '00000') {
          this.merchantData = data.data
        }
      }
    },
    close() {
      this.$refs['form'].resetFields()
      this.dialogFormVisible = false
    },
    save() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          if (this.bindStoreList.length > 0) {
            this.form.bindStore = this.bindStoreList.join(',')
          }
          console.log(this.form)
          if (this.type == 1) {
            const { message } = await doAccountAdd(this.form)
            this.$baseMessage(message, 'success', 'vab-hey-message-success')
          } else {
            const { message } = await doAccountUpdate(this.form)
            this.$baseMessage(message, 'success', 'vab-hey-message-success')
          }
          this.$emit('fetch-data')
          this.close()
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.image-box {
  .image {
    width: 45px;
    height: 45px;
    margin-right: 10px;
    margin-bottom: 10px;
    cursor: pointer;
    border-radius: 100%;
  }

  .active {
    width: 48px;
    height: 48px;
    border: 3px solid #f34d37;
  }
}
</style>
