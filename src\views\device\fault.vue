<template>
  <div class="index-container">
    <!-- begin表格 -->
    <el-table ref="tableSort" v-loading="loading" border :data="tableData">
      <div slot="empty" style="margin: 50px 0">
        <img
          class="img"
          src="https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/image/1865665326720536578.png"
        />
      </div>
      <el-table-column align="center" label="序号">
        <template #default="scope">
          {{
            (queryForm.currentPage - 1) * queryForm.pageSize + scope.$index + 1
          }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="故障类型">
        <template #default="{ row }">
          <div>{{ row.name }}</div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="故障描述">
        <template #default="{ row }">
          <div v-if="row.description">{{ row.description }}</div>
          <div v-else>暂无故障描述</div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="故障时间">
        <template #default="{ row }">
          <div>{{ row.faultTime }}</div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="设备编号">
        <template #default="{ row }">
          <div>{{ row.deviceNumber }}</div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-if="queryForm.total > 10"
      background
      :current-page="queryForm.currentPage"
      :layout="layout"
      :page-size="queryForm.pageSize"
      :total="queryForm.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <!-- end表格 -->
  </div>
</template>

<script>
  import { faultList } from '@/api/device'
  export default {
    name: 'FaultList',
    components: {},
    data() {
      return {
        queryForm: {
          currentPage: 1, // 页码
          pageSize: 20, // 页数
          operType: 0, // 0查询，1导出
          total: 0, // 总条数
        },
        loading: false, // 加载状态
        tableData: [], // 列表数据
        layout: 'total, sizes, prev, pager, next, jumper', // 分页样式
      }
    },
    computed: {},
    beforeMount() {},
    beforeDestroy() {},
    created() {
      this.$nextTick(() => {
        this.fetchData()
      })
    },
    methods: {
      // 查询
      handleQuery() {
        this.queryForm.currentPage = 1
        this.fetchData()
      },
      // 分页
      handleSizeChange(val) {
        this.queryForm.currentPage = val
        this.fetchData()
      },
      // 分页
      handleCurrentChange(val) {
        this.queryForm.currentPage = val
        this.fetchData()
      },
      // 列表查询
      async fetchData() {
        this.loading = true
        const { code, data } = await faultList(this.queryForm)
        if (code == '00000') {
          this.tableData = data.data
          this.queryForm.total = data.paginator.totalRecord
        }
        this.loading = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .link-padding {
    margin-left: 15px;
  }
  .table-column {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .table-column-price {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
</style>
