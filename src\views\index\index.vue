<template>
  <div class="index-container">
    <img class="logo" src="https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/icon/1899663913073524738.png" />
    <p class="text">欢迎进入鲜吧啦平台运营系统</p>
  </div>
</template>

<script>
export default {
  name: 'Index',
  data() {
    return {}
  },
  created() { },
}
</script>

<style lang="scss" scoped>
.index-container {
  width: 100%;
  height: 70vh;
  background-color: #f9f9f9;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.logo {
  width: 300px;
  height: 300px;
  margin-bottom: 40px;
}

.text {
  font-weight: 600;
  font-size: 33px;
  color: transparent;
  letter-spacing: 6px;
  margin: 0;
  padding: 0 16px;
  background: linear-gradient(to right, #000 0, #ffff00 20%, #000 40%);
  background-position: -33px 0;
  background-clip: text;
  animation: eff 3s linear infinite;
}

@keyframes eff {
  to {
    background-position: 363px 0;
  }
}
</style>
