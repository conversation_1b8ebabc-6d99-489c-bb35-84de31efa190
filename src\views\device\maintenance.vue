<template>
  <div class="index-container">
    <!-- 查询条件 -->
    <vab-query-form>
      <vab-query-form-left-panel>
        <el-form :inline="true" label-width="100px" :model="queryForm" size="mini">
          <el-form-item label="配送员姓名">
            <el-input v-model="queryForm.name" :clearable="true" placeholder="请输入配送员姓名" />
          </el-form-item>
          <el-form-item label="配送员电话">
            <el-input v-model="queryForm.phone" :clearable="true" placeholder="请输入配送员电话" />
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="handleQuery">
              查 询
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <!-- 按钮 -->
      <vab-query-form-right-panel>
        <el-form :inline="true" size="mini">
          <el-form-item>
            <el-button icon="el-icon-circle-plus-outline" type="primary" @click="handleAdd">
              添加维保员
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-right-panel>
      <!-- 按钮 -->
    </vab-query-form>
    <!-- 查询条件 -->
    <!-- begin表格 -->
    <el-table ref="tableSort" v-loading="loading" border :data="tableData">
      <div slot="empty" style="margin: 50px">
        <img class="img" src="https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/image/1865665326720536578.png" />
      </div>
      <el-table-column align="center" label="序号" width="100">
        <template #default="scope">
          {{
            (queryForm.currentPage - 1) * queryForm.pageSize + scope.$index + 1
          }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="姓名" min-width="190">
        <template #default="{ row }">
          <div>{{ row.name }}</div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="IUD" min-width="190">
        <template #default="{ row }">
          <div>{{ row.name }}</div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="电话" min-width="190">
        <template #default="{ row }">
          <div>{{ row.phone }}</div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="负责的设备" min-width="190">
        <template #default="{ row }">
          <div>
            <el-tag>{{ row.devices.length }}台</el-tag>
            <span>&nbsp;&nbsp;</span>
            <el-link type="primary" @click="handleSelect(row)">查看</el-link>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column align="center" label="启用状态">
        <template #default="{ row }">
          <el-switch v-model="row.enabled" @change="changeSwitch(row)" />
        </template>
      </el-table-column> -->
      <el-table-column align="center" fixed="right" label="操作" width="200">
        <template #default="{ row }">
          <p>
            <el-button type="primary" icon="el-icon-edit" plain @click="handleEdit(row)">编辑信息</el-button>
          </p>
          <p>
            <el-button type="success" icon="el-icon-thumb" plain @click="handleAllocateDevices(row)">分配设备</el-button>
          </p>
          <p>
          <p>
            <el-button type="info" icon="el-icon-notebook-2" plain @click="handleDeliveryRecords(row)">配送记录</el-button>
          </p>
          </p>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background :current-page="queryForm.currentPage" :layout="layout" :page-size="queryForm.pageSize"
      :total="queryForm.total" @current-change="handleCurrentChange" @size-change="handleSizeChange" />
    <!-- end表格 -->


    <!-- 添加/编辑维保员弹窗 -->
    <el-dialog :title="isEdit ? '编辑维保员' : '添加维保员'" :visible.sync="deliveryDialogVisible" width="30%" center>
      <el-form ref="deliveryForm" :model="deliveryForm" :rules="deliveryRules" label-width="80px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="deliveryForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="deliveryForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="类型" prop="phone">
          <el-checkbox-group v-model="checkList" @change="handleCheckListChange" size="mini">
            <el-checkbox label="1">维保员</el-checkbox>
            <el-checkbox label="2">巡检员</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deliveryDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveDeliveryman">保存</el-button>
      </span>
    </el-dialog>
    <!-- 分配设备弹窗 -->
    <el-dialog title="分配设备" :visible.sync="allocateDialogVisible" width="50%" center>
      <div class="allocate-header">
        <div>配送员：{{ currentDeliveryman.name }}</div>
      </div>
      <div class="allocate-content">
        <p>选择设备</p>
        <el-transfer v-model="selectedDevices" :data="allDevices" :titles="['未分配设备', '已分配设备']"
          :button-texts="['移除', '添加']" :format="{
            noChecked: '${total}',
            hasChecked: '${checked}/${total}'
          }" @change="handleDeviceChange" :props="{
            key: 'id',
            label: 'name'
          }" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="allocateDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAllocate">确认分配</el-button>
      </span>
    </el-dialog>
    <!-- 配送记录弹窗 -->
    <el-dialog title="配送记录" :visible.sync="recordsDialogVisible" width="60%" center>
      <div class="records-header">
        <div>配送员：{{ currentDeliverymanName }}</div>
      </div>
      <el-table v-loading="recordsLoading" :data="currentDeliveryRecords" border style="width: 100%">
        <el-table-column prop="deliveryTime" label="配送时间" width="180" />
        <el-table-column prop="deviceName" label="配送设备" width="180" />
        <el-table-column prop="dishName" label="配送餐品" />
        <el-table-column prop="stockChange" label="库存变化" />
      </el-table>
      <div v-if="currentDeliveryRecords.length === 0 && !recordsLoading" class="no-records">
        <el-empty description="暂无配送记录" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="recordsDialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!-- 在template底部添加TableSelect组件 -->
    <d-table-select ref="select" @refresh-list="fetchData" />
  </div>
</template>



<!-- 在script部分引入组件 -->
<script>
import { maintenanceList, addMaintenance, updateMaintenance, unassignedDevices, allocationDevices, assignDevices, deliveryRecords } from '@/api/device'
import DTableSelect from './components/DTableSelect.vue'

export default {
  name: 'Delivery',
  components: {
    DTableSelect
  },
  data() {
    return {
      isEdit: false, // 是否为编辑模式
      createTime: '', // 创建时间
      queryForm: {
        currentPage: 1, // 页码
        pageSize: 10, // 页数
        operType: 0, // 0查询，1导出
        total: 0, // 总条数
        name: '', // 配送员名称
        phone: '', // 配送员手机号
      },
      checkList: [], // 维保员类型
      loading: false,
      tableData: [],
      layout: 'total, sizes, prev, pager, next, jumper',
      deliveryDialogVisible: false, // 添加配送员弹窗
      deliveryForm: {
        name: '', // 配送员姓名
        phone: '', // 配送员手机号
        type: '', // 类型：1-维保员，2-巡检员，多个类型用逗号分隔，如：1,2
      },
      deliveryRules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
        ]
      },
      allocateDialogVisible: false, // 分配设备弹窗
      currentDeliveryman: {}, // 当前选中的配送员
      allDevices: [], // 所有设备列表（用于Transfer组件）
      selectedDevices: [], // 已选择的设备ID列表
      recordsDialogVisible: false, // 配送记录弹窗
      currentDeliveryRecords: [], // 当前配送员的配送记录
      recordsLoading: false, // 配送记录加载状态
      currentDeliverymanName: '', // 当前查看记录的配送员姓名
    }
  },
  computed: {},
  beforeMount() { },
  beforeDestroy() { },
  created() {
    this.$nextTick(() => {
      // 获取设备列表
      this.fetchData()
    })
  },
  methods: {
    // 处理维保员类型选择变化
    handleCheckListChange(value) {
      this.deliveryForm.type = value.join(',')
    },
    // 获取配送员记录
    async handleDeliveryRecords(row) {
      try {
        this.recordsLoading = true
        const res = await deliveryRecords({ id: row.id })
        if (res.code === '00000') {
          this.currentDeliveryRecords = res.data
          this.currentDeliverymanName = row.name
          this.recordsDialogVisible = true
        } else {
          this.$message.error('获取配送记录失败')
        }
      } catch (error) {
        console.error('获取配送记录失败:', error)
        this.$message.error('获取配送记录失败')
      } finally {
        this.recordsLoading = false
      }
    },
    // 添加配送员
    handleAdd() {
      this.deliveryForm = {
        name: '',
        phone: ''
      }
      this.deliveryDialogVisible = true
      // 设置当前操作为添加
      this.isEdit = false // 是否为编辑模式
    },

    // 编辑配送员信息
    handleEdit(row) {
      this.deliveryForm = {
        id: row.id,
        name: row.name,
        phone: row.phone
      }
      this.deliveryDialogVisible = true
      // 设置当前操作为编辑
      this.isEdit = true
    },

    // 保存配送员
    saveDeliveryman() {
      this.$refs.deliveryForm.validate(async (valid) => {
        if (valid) {
          try {
            let response;
            if (this.isEdit) {
              // 编辑模式
              response = await updateMaintenance(this.deliveryForm);
            } else {
              // 添加模式
              response = await addMaintenance(this.deliveryForm);
            }

            const { code } = response;
            if (code === '00000') {
              this.$message.success(this.isEdit ? '更新维保员成功' : '添加维保员成功');
              this.deliveryDialogVisible = false;
              this.fetchData(); // 刷新列表
            } else {
              this.$message.error(this.isEdit ? '更新维保员失败' : '添加维保员失败');
            }
          } catch (error) {
            console.error(this.isEdit ? '更新维保员失败:' : '添加维保员失败:', error);
            this.$message.error(this.isEdit ? '更新维保员失败' : '添加维保员失败');
          }
        }
      });
    },
    // 选择日期
    onDateRange(e) {
      this.queryForm.createTimeBegin = this.formatDateToString(e[0])
      this.queryForm.createTimeEnd = this.formatDateToString(e[1])
      // 查询
      this.handleQuery()
    },
    // 日期转换成字符串
    formatDateToString(dateString) {
      // 创建一个新的 Date 对象，传入你的日期字符串
      const date = new Date(dateString)
      // 检查是否创建了有效的日期对象
      if (isNaN(date.getTime())) {
        throw new Error('Invalid date string')
      }
      // 提取年份、月份（注意：getMonth() 返回的月份是从0开始的，所以需要加1）、日期
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0') // 确保两位数显示
      const day = String(date.getDate()).padStart(2, '0') // 确保两位数显示

      // 拼接成你想要的格式并返回
      return `${year}-${month}-${day}`
    },
    // 字符串转数组
    stringToArray(e) {
      return e.split(',')
    },
    // 随机type
    randomType() {
      let fruits = ['primary', 'success', 'warning', 'danger', 'info']
      let randomFruit = fruits[Math.floor(Math.random() * fruits.length)]
      return randomFruit
    },
    //查询
    handleQuery() {
      this.queryForm.currentPage = 1
      this.fetchData()
    },
    // 分页
    handleSizeChange(val) {
      this.queryForm.currentPage = val
      this.fetchData()
    },
    // 分页
    handleCurrentChange(val) {
      this.queryForm.currentPage = val
      this.fetchData()
    },
    //列表查询
    async fetchData() {
      this.loading = true
      const { data } = await maintenanceList(this.queryForm)
      this.tableData = data.data
      this.queryForm.total = data.paginator.totalRecord
      this.loading = false
    },
    // 查看设备
    handleSelect(row) {
      // 显示设备详情信息
      this.$refs['select'].show(row)
    },
    // 打开分配设备弹窗
    async handleAllocateDevices(row) {
      this.currentDeliveryman = row;
      this.allocateDialogVisible = true;

      try {
        // 获取未分配设备
        const unassignedRes = await unassignedDevices();
        // 获取已分配给该配送员的设备
        const allocatedRes = await allocationDevices({ id: row.id });

        if (unassignedRes.code === '00000' && allocatedRes.code === '00000') {
          // 构建所有设备列表
          const unassignedDevicesList = unassignedRes.data.map(device => ({
            id: device.deviceId,
            name: `设备${device.deviceId}`,
            disabled: false
          }));

          const allocatedDevicesList = allocatedRes.data.map(device => ({
            id: device.deviceId,
            name: `设备${device.deviceId}`,
            disabled: true
          }));

          // 合并设备列表
          this.allDevices = [...unassignedDevicesList, ...allocatedDevicesList];

          // 设置已分配的设备ID
          this.selectedDevices = allocatedDevicesList.map(device => device.id);
        } else {
          this.$message.error('获取设备列表失败');
        }
      } catch (error) {
        console.error('获取设备列表失败:', error);
        this.$message.error('获取设备列表失败');
      }
    },

    // 处理设备选择变化
    handleDeviceChange(value, direction, movedKeys) {
      console.log(value, direction, movedKeys);
    },

    // 确认分配设备
    async confirmAllocate() {
      try {
        const params = {
          deliverymanId: this.currentDeliveryman.id,
          deviceNos: this.selectedDevices
        };

        // 这里需要根据实际API调整
        const res = await assignDevices(params);

        if (res.code === '00000') {
          this.$message.success('设备分配成功');
          this.allocateDialogVisible = false;
          this.fetchData(); // 刷新列表
        } else {
          this.$message.error('设备分配失败');
        }
      } catch (error) {
        console.error('设备分配失败:', error);
        this.$message.error('设备分配失败');
      }
    },

  },
}
</script>

<style lang="scss" scoped>
.link-padding {
  margin-left: 15px;
}

.table-column {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.table-empty {
  margin: 50px 0;
}

.table-column-price {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.device-info {
  min-width: 240px;
  display: flex;
  flex-direction: column;

  p {
    margin: 0;
    text-align: left;
  }
}

.qrcode-cont {
  display: flex;
  align-items: center;
  justify-content: center;
}

.network-info {
  min-width: 160px;
  display: flex;
  flex-direction: column;

  p {
    margin: 0;
    text-align: left;
  }
}

.food-info {
  min-width: 170px;
  display: flex;
  flex-direction: column;

  p {
    margin: 0;
    text-align: left;
  }
}

.allocate-header {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
}

.allocate-content {
  margin-bottom: 20px;
}

.allocate-content p {
  margin-bottom: 10px;
  font-weight: bold;
}

.el-transfer {
  text-align: left;
  display: flex;
  justify-content: center;
}

.records-header {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
}

.no-records {
  margin: 20px 0;
  display: flex;
  justify-content: center;
}
</style>
