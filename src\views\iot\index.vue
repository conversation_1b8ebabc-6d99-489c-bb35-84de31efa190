<template>
  <!-- 积分商城 管理 -->
  <div class="index-container">
    <!-- 查询条件 -->
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form ref="form" :inline="true" label-width="88px" :model="queryForm">
          <el-form-item label="DTU ID">
            <el-input v-model="queryForm.id" :clearable="true" placeholder="请输入DTU设备ID" />
          </el-form-item>
          <el-form-item label="绑定状态">
            <el-select v-model="queryForm.isBound" filterable placeholder="请选择绑定状态">
              <el-option label="全部" value="" />
              <el-option label="未绑定" :value="0" />
              <el-option label="已绑定" value="1" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="handleQuery">
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <!-- 按钮 -->
      <vab-query-form-top-panel>
        <el-button icon="el-icon-circle-plus-outline" type="primary" @click="handleAdd">
          新增DTU设备
        </el-button>
      </vab-query-form-top-panel>
    </vab-query-form>
    <!-- begin表格 -->
    <el-table ref="tableSort" v-loading="loading" border :data="tableData">
      <div slot="empty" style="margin: 50px 0">
        <img class="img" src="https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/image/1865665326720536578.png" />
      </div>
      <el-table-column align="center" label="序号" show-overflow-tooltip width="90">
        <template #default="scope">
          {{
          (queryForm.currentPage - 1) * queryForm.pageSize + scope.$index + 1
        }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="DTU ID" prop="id" width="180" />
      <el-table-column align="center" label="在线状态" width="120">
        <template #default="{ row }">
          <div v-if="row.onlineStatus">
            <el-tag v-if="row.onlineStatus == 'online'" type="success">在线</el-tag>
            <el-tag v-if="row.onlineStatus == 'offline'" type="warning">离线</el-tag>
          </div>
          <div v-else>
            <el-tag type="warning">离线</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="网络类型" width="120">
        <template #default="{ row }">
          <div v-if="row.netWorkType == null"></div>
          <div v-else>
            <div v-if="row.netWorkType == '1'">LAN</div>
            <div v-if="row.netWorkType == '2'">Wi-Fi</div>
            <div v-if="row.netWorkType == '8'">4G</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="SIM卡（ICCID）">
        <template #default="{ row }">
          <div v-if="row.simCard">
            <span>{{ row.simCard }}</span>
            <!-- <el-button
              plain
              style="margin-left: 10px"
              type="primary"
              @click="getUpdates"
            >
              更新
            </el-button> -->
          </div>
          <!-- <div v-else>
            <el-button
              plain
              style="margin-left: 10px"
              type="success"
              @click="getUpdates"
            >
              获取
            </el-button>
          </div> -->
        </template>
      </el-table-column>
      <el-table-column align="center" label="信号强度" width="120">
        <template #default="{ row }">
          <div v-if="row.signalStrength">
            <div v-if="row.netWorkType == '2'">
              <span v-if="row.signalStrength < 40">
                信号异常（{{ row.signalStrength }}）
              </span>
              <span v-if="row.signalStrength > 40 && row.signalStrength < 71">
                正常（{{ row.signalStrength }}）
              </span>
              <span v-if="row.signalStrength == 99">
                无信号
              </span>
            </div>
            <div v-if="row.netWorkType == '8'">
              <span v-if="row.signalStrength < 10">
                信号异常（{{ row.signalStrength }}）
              </span>
              <span v-if="row.signalStrength > 9 && row.signalStrength < 32">
                正常（{{ row.signalStrength }}）
              </span>
              <span v-if="row.signalStrength == 99">
                无信号
              </span>
            </div>
          </div>
          <div v-else>
            <span></span>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="是否绑定设备" width="130">
        <template #default="{ row }">
          <div v-if="row.isBound">
            <span>已绑定</span>
          </div>
          <div v-else>
            <span>暂未绑定</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="绑定设备编号" width="150">
        <template #default="{ row }">
          <div v-if="row.deviceId">
            <span>{{ row.deviceId }}</span>
          </div>
          <div v-else>
            <el-button plain type="success" @click="handBinding(row, 1)">
              绑定设备
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="150">
        <template #default="{ row }">
          <div>
            <el-link type="primary" @click="handEdit(row)">编辑</el-link>
          </div>
          <div v-if="row.isBound">
            <el-link type="warning" @click="handUnbind(row)">解绑</el-link>
          </div>
          <div v-else>
            <el-link type="success" @click="handBinding(row, 1)">绑定</el-link>
          </div>
          <div>
            <el-link type="primary" @click="handleDelete(row, 3)">删除</el-link>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-if="queryForm.total > 0" background :current-page="queryForm.currentPage" :layout="layout"
      :page-size="queryForm.pageSize" :total="queryForm.total" @current-change="handleCurrentChange"
      @size-change="handleSizeChange" />
    <!-- end表格 -->
    <!-- 新增DTU设备弹窗 -->
    <el-dialog center :title="dialogTitle" :visible.sync="addDtuDialogVisible" @closed="dialogClosed" width="30%">
      <el-form label-position="right" label-width="75px" :model="form">
        <el-form-item label="DTU ID" required>
          <el-input v-model="form.id" :clearable="true" placeholder="请输入DTU设备ID" />
        </el-form-item>
        <el-form-item label="设备编号">
          <el-input v-model="form.deviceId" :disabled="isDisable" :clearable="true" placeholder="请输入设备编号" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addDtuDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleAddDtu">
          {{ btnText }}
        </el-button>
      </span>
    </el-dialog>
    <!-- 绑定DTU设备弹窗 -->
    <el-dialog center title="绑定设备" :visible.sync="bindingDtuDialogVisible" width="30%">
      <el-form label-position="right" label-width="75px" :model="bindingForm">
        <el-form-item label="设备ID" required>
          <el-input v-model="bindingForm.deviceId" :clearable="true" placeholder="请输入设备ID" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="bindingDtuDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleBindingDtu">确定绑定设备</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { dtuList, addDtu, editDtu, deleteDtu, bindDtu, unbindDtu } from '@/api/iot'
export default {
  name: 'IotIndex',
  components: {},
  data() {
    return {
      loading: false, // 加载状态
      btnText: '确定新增', // 按钮文字
      dialogTitle: '新增DTU设备', // 新增DTU设备标题
      addDtuDialogVisible: false, // 新增DTU设备弹窗
      bindingDtuDialogVisible: false, // 绑定DTU设备弹窗
      submitType: 1, // 提交类型：1新增，2编辑
      queryForm: {
        currentPage: 1, // 页码
        pageSize: 20, // 页数
        total: 0, // 总页数
        id: '', // DTU设备ID
        isBound: '', // 是否绑定设备：0未绑定，1已绑定
      },
      form: {
        dtuId: '', // ID
        deviceId: '', // 设备编号
      },
      bindingForm: {
        deviceId: '', // 设备ID
        id: '', // DTU设备ID
      },
      tableData: [], // 表格数据
      layout: 'total, sizes, prev, pager, next, jumper', // 分页配置
      isDisable: false, // 是否禁用
    }
  },
  computed: {},
  created() {
    this.$nextTick(() => {
      this.fetchData()
    })
  },
  methods: {
    // 获取更新 sin 卡
    getUpdates() {
      this.loading = true // 加载状态
      setTimeout(() => {
        this.handleQuery()
      }, 2000)
    },
    // 绑定或更新SIM卡
    handBinding(row, type) {
      console.log(row)
      this.submitType = 2
      if (type == 1) {
        this.bindingForm.id = row.id
        this.bindingDtuDialogVisible = true
      } else if (type == 2) {
        this.btnText = '确定更新'
        this.dialogTitle = '修改DTU设备'
        this.form.id = row.id
        this.form.dtuId = row.dtuId
        this.form.deviceId = row.deviceId
        this.addDtuDialogVisible = true
      }

    },
    // 确定绑定设备
    async handleBindingDtu() {
      if (this.bindingForm.deviceId == '') {
        this.$baseMessage('请输入设备ID', 'warning')
        return false
      } else {
        const { code } = await bindDtu(this.bindingForm)
        if (code == '00000') {
          this.$message({
            type: 'success',
            message: '绑定成功',
          })
          this.bindingDtuDialogVisible = false
          this.handleQuery()
        }
      }
    },
    // 关闭弹窗
    dialogClosed() {
      this.isDisable = false
    },
    // 查询
    handleQuery() {
      this.queryForm.currentPage = 1
      this.fetchData()
    },
    // 分页
    handleSizeChange(val) {
      this.queryForm.currentPage = val
      this.fetchData()
    },
    // 分页
    handleCurrentChange(val) {
      this.queryForm.currentPage = val
      this.fetchData()
    },
    // 列表查询
    async fetchData() {
      this.loading = true
      const { data, code } = await dtuList(this.queryForm)
      if (code == '00000') {
        this.tableData = data.data
        this.queryForm.total = data.paginator.totalRecord
        this.loading = false
      }
    },
    // 添加DTU设备
    handleAdd() {
      this.form = {
        id: '', // DTU设备ID
        deviceId: '', // 设备编号
      }
      this.submitType = 1
      this.btnText = '确定新增'
      this.dialogTitle = '新增DTU设备'
      this.addDtuDialogVisible = true
    },
    // 确定新增DTU设备
    async handleAddDtu() {
      if (!this.form.id) {
        this.$baseMessage('请输入DTU设备ID', 'warning')
        return false
      } else {
        let rqt = null
        if (this.submitType == 1) {
          rqt = addDtu
        } else if (this.submitType == 2) {
          rqt = editDtu
        }
        let { code, data } = await rqt(this.form)
        console.log(data)
        if (code == '00000') {
          this.$baseMessage('操作成功', 'success', 'vab-hey-message-success')
          this.addDtuDialogVisible = false
          this.handleQuery()
        }
      }
    },
    // 编辑
    handEdit(row) {
      this.submitType = 2
      this.btnText = '确定编辑'
      this.dialogTitle = '修改DTU设备'
      this.isDisable = true
      this.form.id = row.id
      this.form.deviceId = row.deviceId
      this.addDtuDialogVisible = true
    },
    // 解绑
    handUnbind(row) {
      console.log(row)
      this.$baseConfirm(`你确定解绑ID：${row.id}的设备吗？`, null, async () => {
        const { message } = await unbindDtu({
          id: row.id,
          deviceId: row.deviceId,
        })
        console.log(message)
        this.$baseMessage('操作成功', 'success', 'vab-hey-message-success')
        await this.fetchData()
      })
    },
    // 删除
    handleDelete(row) {
      let confimText = `你确定删除DTUID：${row.dtuId}吗？`
      this.$baseConfirm(`${confimText}`, null, async () => {
        const { message } = await deleteDtu({ id: Number(row.id) })
        console.log(message)
        this.$baseMessage('操作成功', 'success', 'vab-hey-message-success')
        await this.fetchData()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.link-padding {
  margin-left: 15px;
}

.table-column {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.table-column-price {
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
