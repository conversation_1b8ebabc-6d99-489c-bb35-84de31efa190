<template>
  <div class="login-container">
    <div class="right-form">
      <h1 class="title">鲜吧啦平台运营系统</h1>
      <el-tabs v-model="activeName">
        <el-tab-pane label="" name="admin">
          <el-form ref="form" class="login-form" label-position="left" :model="form" :rules="rules">
            <el-form-item prop="account">
              <el-input v-model.trim="form.account" v-focus :placeholder="translateTitle('请输入管理员账号')" size="medium"
                tabindex="1" type="text">
                <template #prefix>
                  <vab-icon icon="user-line" />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item prop="passWord">
              <el-input :key="passwordType" ref="password" v-model.trim="form.passWord"
                :placeholder="translateTitle('请输入密码')" size="medium" tabindex="2" :type="passwordType"
                @keyup.enter.native="handleLogin">
                <template #prefix>
                  <vab-icon icon="lock-line" />
                </template>
                <template v-if="passwordType === 'password'" #suffix>
                  <vab-icon class="show-password" icon="eye-off-line" @click="handlePassword" />
                </template>
                <template v-else #suffix>
                  <vab-icon class="show-password" icon="eye-line" @click="handlePassword" />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button class="login-btn" :loading="loading" size="medium" type="primary" @click="handleLogin">
                {{ translateTitle('登录') }}
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <!-- <el-tab-pane label="商户登录" name="trader">
          <el-form ref="traderForm" class="login-form" label-position="left" :model="traderForm" :rules="rules">
            <el-form-item prop="account">
              <el-input v-model.trim="traderForm.account" v-focus :placeholder="translateTitle('请输入商户账号或手机号')"
                size="medium" tabindex="1" type="text">
                <template #prefix>
                  <vab-icon icon="user-line" />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item prop="passWord">
              <el-input :key="passwordType" ref="password" v-model.trim="traderForm.passWord"
                :placeholder="translateTitle('请输入密码')" size="medium" tabindex="2" :type="passwordType"
                @keyup.enter.native="handleLogin">
                <template #prefix>
                  <vab-icon icon="lock-line" />
                </template>
                <template v-if="passwordType === 'password'" #suffix>
                  <vab-icon class="show-password" icon="eye-off-line" @click="handlePassword" />
                </template>
                <template v-else #suffix>
                  <vab-icon class="show-password" icon="eye-line" @click="handlePassword" />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button class="login-btn" :loading="loading" size="medium" type="primary" @click="handleLogin">
                {{ translateTitle('登录') }}
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane> -->
      </el-tabs>
      <div class="login-tips">
        <span class="text">建议使用：</span>
        <img class="icon" src="~@/assets/login_images/icon/1.png" />
        <span class="text">Chrome、</span>
        <img class="icon" src="~@/assets/login_images/icon/6.png" />
        <span class="text">Firefox、</span>
        <img class="icon" src="~@/assets/login_images/icon/2.png" />
        <span class="text">Safari、</span>
        <img class="icon" src="~@/assets/login_images/icon/3.png" />
        <span class="text">360、</span>
        <img class="icon" src="~@/assets/login_images/icon/4.png" />
        <span class="text">搜狗、</span>
        <img class="icon" src="~@/assets/login_images/icon/5.png" />
        <span class="text">QQ等浏览器</span>
      </div>
    </div>
    <el-dialog title="请选择商户进行登录" :visible.sync="selectMerchantDialog" width="30%" center>
      <el-radio-group class="radio-group" v-model="userId" size="small">
        <div class="radio-box" v-for="(item, index) in accounts" :key="index">
          <el-radio :label="item.userId" border>
            <span>商户ID：{{ item.userId }}</span>&nbsp;&nbsp;&nbsp;&nbsp;
            <span>商户名称：{{ item.userName }}</span>
          </el-radio>
        </div>

      </el-radio-group>
      <div slot="footer">
        <el-button @click="selectMerchantDialog = false">取 消</el-button>
        <el-button type="primary" size="mini" @click="confirmLogin">确定登录</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { translateTitle } from '@/utils/i18n'
import { isPassword } from '@/utils/validate'
import { accountLoginCheck } from '@/api/user'
import Vue from 'vue'
import { title } from '@/config'
export default {
  name: 'Login',
  directives: {
    focus: {
      inserted(el) {
        el.querySelector('input').focus()
      },
    },
  },
  beforeRouteLeave(to, from, next) {
    clearInterval(this.timer)
    next()
  },
  data() {
    const validateUsername = (rule, value, callback) => {
      if ('' === value)
        callback(new Error(this.translateTitle('账号不能为空')))
      else callback()
    }
    const validatePassword = (rule, value, callback) => {
      if (!isPassword(value))
        callback(new Error(this.translateTitle('密码不能少于6位')))
      else callback()
    }
    return {
      activeName: 'admin', // 默认登录类型
      selectMerchantDialog: false, // 商户选择弹窗
      accounts: [], // 商户列表
      userId: '', // 用户 ID
      form: {
        account: '', // 账号
        passWord: '', // 密码
        loginType: 1, // 登录类型
      },
      traderForm: {
        // account: '***********', // 账号
        // account: '***********', // 账号
        account: '', // 账号
        passWord: '', // 密码
        loginType: 2, // 登录类型

      },
      rules: {
        account: [
          {
            required: true,
            trigger: 'blur',
            validator: validateUsername,
          },
        ],
        passWord: [
          {
            required: true,
            trigger: 'blur',
            validator: validatePassword,
          },
        ],
        /* verificationCode: [
          {
            required: true,
            trigger: 'blur',
            message: '验证码不能空',
          },
        ], */
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined,
      timer: 0,
      codeUrl: 'https://www.oschina.net/action/user/captcha',
      previewText: '',
    }
  },
  computed: {
    ...mapGetters({
      title: 'settings/title',
    }),
  },
  watch: {
    $route: {
      handler(route) {
        this.redirect = (route.query && route.query.redirect) || '/'
      },
      immediate: true,
    },
  },
  mounted() {
    this.form.account = ''
    this.form.passWord = ''
    // this.traderForm.account = ''
    // this.traderForm.passWord = ''
  },
  methods: {
    ...mapActions({
      login: 'user/login',
      setToken: 'user/setToken'
    }),
    translateTitle,
    handlePassword() {
      this.passwordType === 'password'
        ? (this.passwordType = '')
        : (this.passwordType = 'password')
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleRoute() {
      return this.redirect === '/404' || this.redirect === '/403'
        ? '/'
        : this.redirect
    },
    handleLogin() {
      if (this.activeName == 'admin') {
        this.$refs.form.validate(async (valid) => {
          if (valid)
            try {
              this.loading = true
              await this.login(this.form).catch(() => { })
              await this.$router.push(this.handleRoute())
            } finally {
              this.loading = false
            }
        })
      } else {
        this.$refs.traderForm.validate(async (valid) => {
          if (valid)
            try {
              this.loading = true
              const { code, data } = await accountLoginCheck(this.traderForm)
              if (code == '10001') {
                this.accounts = data.accounts
                this.selectMerchantDialog = true
              } else {
                this.setToken(data.loginToken)
                await this.$router.push(this.handleRoute())
                const hour = new Date().getHours()
                const thisTime =
                  hour < 8
                    ? '早上好'
                    : hour <= 11
                      ? '上午好'
                      : hour <= 13
                        ? '中午好'
                        : hour < 18
                          ? '下午好'
                          : '晚上好'
                Vue.prototype.$baseNotify(`欢迎登录${title}`, `${thisTime}！`)
              }
            } finally {
              this.loading = false
            }
        })
      }
    },
    // 确定登录
    confirmLogin() {
      if (this.userId) {
        this.$refs.traderForm.validate(async (valid) => {
          if (valid)
            try {
              this.loading = true
              await this.login({
                userId: this.userId,
                password: this.traderForm.passWord,
                loginType: this.traderForm.loginType
              }).catch(() => { })
              await this.$router.push(this.handleRoute())
            } finally {
              this.loading = false
            }
        })
      } else {
        this.$message.error('请选择登录账号');
      }
    },
    changeCode() {
      this.codeUrl = `https://www.oschina.net/action/user/captcha?timestamp=${new Date().getTime()}`
    },
  },
}
</script>

<style lang="scss" scoped>
.login-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-image: url('~@/assets/login_images/background.jpg');
  background-position: center;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .radio-group {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .radio-box {
    margin: 6px auto;

    .radio-ls {
      width: 500px;
      margin: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .right-form {
    width: 600px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 60px 30px;
    background-color: #fff;
    box-shadow: 0 0 10px #ccc;
    border-radius: 5px;
    margin-right: 10%;

    .title {
      text-align: center;
      margin-bottom: 40px;
    }

    .login-form {
      width: 100%;

      .login-btn {
        width: 100%;
      }
    }

    .login-tips {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-top: 30px;

      .text {
        font-size: 14px;
        color: #606266;
      }

      .icon {
        width: 20px;
        height: 20px;
        margin-right: 2px;
      }
    }
  }
}
</style>
