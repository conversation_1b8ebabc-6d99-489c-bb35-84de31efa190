import request from '@/utils/request'

// 管理员登录接口
export async function login(data) {
  return request({
    url: '/user/login',
    method: 'post',
    data,
  })
}

// 商户登录接口
export async function TwoLogin(data) {
  return request({
    url: '/user/loginByUserId',
    method: 'post',
    data,
  })
}

// 账号登录检测
export async function accountLoginCheck(data) {
  return request({
    url: '/user/accountLoginCheck',
    method: 'post',
    data,
  })
}

// 根据token查询个人信息接口
export function getUserInfo(data) {
  return request({
    url: '/user-role/getRoles',
    method: 'post',
    data
  })
}
// 退出接口
export function logout() {
  return request({
    url: '/backend/login/logout',
    method: 'post',
  })
}
// 注册接口
export function register(data) {
  return request({
    url: '/register',
    method: 'post',
    data,
  })
}

// 个人中心  修改密码功能
export function loginPwd(data) {
  return request({
    url: '/backend/login/pwd',
    method: 'post',
    data,
  })
}

// 用户列表
export function userList(data) {
  return request({
    url: '/user/customerPageList',
    method: 'post',
    data,
  })
}

// 用户详情
export function userDetails(data) {
  return request({
    url: '/user/detail',
    method: 'post',
    data,
  })
}
