import request from '@/utils/request'

// 菜品列表
export async function pageList(data) {
  return request({
    url: '/dish/pageList',
    method: 'post',
    data,
  })
}

// 新增菜品
export async function addFood(data) {
  return request({
    url: '/dish/save',
    method: 'post',
    data,
  })
}

// 修改菜品
export async function editFood(data) {
  return request({
    url: '/dish/update',
    method: 'post',
    data,
  })
}

// 菜品详情
export async function getFoodDetails(data) {
  return request({
    url: '/dish/get',
    method: 'post',
    data,
  })
}

// 新增菜品类型
export async function addFoodType(data) {
  return request({
    url: '/dish-category/save',
    method: 'post',
    data,
  })
}

// 更新菜品类型
export async function updateFoodType(data) {
  return request({
    url: '/dish-category/update',
    method: 'post',
    data,
  })
}

// 菜品类型列表
export async function foodTypeList(data) {
  return request({
    url: '/dish-category/pageList',
    method: 'post',
    data,
  })
}
