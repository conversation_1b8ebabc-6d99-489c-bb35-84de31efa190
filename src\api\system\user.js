import request from '@/utils/request'

//账号列表
export function doAccountList(data) {
  return request({
    url: '/user-role/pageList',
    method: 'post',
    data,
  })
}
//修改
export function doAccountUpdate(data) {
  return request({
    url: '/backend/account/update',
    method: 'post',
    data,
  })
}
//修改密码
export function doAccountUpdatePwd(data) {
  return request({
    url: '/backend/account/pwd',
    method: 'post',
    data,
  })
}
//添加
export function doAccountAdd(data) {
  return request({
    url: '/backend/account/add',
    method: 'post',
    data,
  })
}
//删除
export function doAccountDelete(data) {
  return request({
    url: '/backend/account/delete',
    method: 'post',
    data,
  })
}
//详情
export function doAccountFindById(data) {
  return request({
    url: '/backend/account/findById',
    method: 'post',
    data,
  })
}

// 商户分页查询
export function merchantList(data) {
  return request({
    url: '/merchant-info/pageList',
    method: 'post',
    data,
  })
}
