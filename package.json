{"name": "admin-pro", "version": "2.0.9", "private": true, "author": "chuz<PERSON><PERSON>", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "lint:eslint": "eslint {src,mock}/**/*.{vue,js} --fix", "lint:prettier": "prettier {src,mock}/**/*.{html,vue,css,sass,scss,js,md} --write", "lint:stylelint": "stylelint {src,mock}/**/*.{html,vue,css,sass,scss} --fix --cache --cache-location node_modules/.cache/stylelint/", "template": "plop"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@riophae/vue-treeselect": "^0.4.0", "axios": "^0.24.0", "core-js": "^3.19.3", "dayjs": "^1.10.7", "element-china-area-data": "^6.1.0", "element-ui": "2.15.6", "js-cookie": "^3.0.1", "jsencrypt": "^3.2.1", "jsplumb": "^2.15.6", "lodash": "^4.17.21", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "qs": "^6.10.2", "quill-image-resize-module": "^3.0.0", "resize-detector": "^0.3.0", "screenfull": "5.2.0", "vab-icons": "^0.0.23", "vue": "^2.6.14", "vue-i18n": "^8.26.7", "vue-print-nb": "^1.7.5", "vue-quill-editor": "^3.0.6", "vue-router": "^3.5.3", "vuedraggable": "^2.24.3", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.5.15", "@vue/cli-plugin-eslint": "^4.5.15", "@vue/cli-plugin-router": "^4.5.15", "@vue/cli-plugin-vuex": "^4.5.15", "@vue/cli-service": "^4.5.15", "@vue/eslint-config-prettier": "^6.0.0", "body-parser": "^1.19.1", "chalk-next": "^6.1.5", "chokidar-next": "^4.0.10", "compression-webpack-plugin": "6.1.1", "eslint": "7.32.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.2.0", "filemanager-webpack-plugin": "3.1.1", "image-webpack-loader": "^8.0.1", "lint-staged": "^12.1.2", "plop": "^3.0.5", "postcss-html": "^1.3.0", "postcss-jsx": "^0.36.4", "postcss-scss": "^4.0.2", "postcss-syntax": "^0.36.2", "prettier": "^2.5.1", "quill-image-extend-module": "^1.1.2", "raw-loader": "^4.0.2", "sass": "1.32.13", "sass-loader": "10.2.0", "stylelint": "^14.1.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recess-order": "^3.0.0", "svg-sprite-loader": "^6.0.11", "vab-templates": "^0.0.5", "vue-eslint-parser": "^8.0.1", "vue-template-compiler": "^2.6.14", "webpackbar": "^5.0.2"}, "gitHooks": {"pre-commit": "lint-staged"}, "homepage": "https://chu1204505056.gitee.io/admin-pro", "license": "Mozilla Public License Version 2.0", "lint-staged": {"*.{js,jsx,vue}": ["vue-cli-service lint", "git add"]}, "participants": ["fwfmiao"], "repository": {"type": "git", "url": "git+https://github.com/vue-admin-beautiful/admin-pro.git"}}