<template>
  <div class="index-container">
    <!-- 查询条件 -->
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form :inline="true" label-width="100px" :model="queryForm" size="mini">
          <el-form-item label="性别">
            <el-select v-model="queryForm.sex" placeholder="请选择性别">
              <el-option label="全部" value="" />
              <el-option label="男" value="1" />
              <el-option label="女" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="手机号码">
            <el-input v-model="queryForm.phone" :clearable="true" placeholder="请输入手机号码" />
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="handleQuery">
              查 询
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <!-- 按钮 -->
    </vab-query-form>
    <!-- begin表格 -->
    <el-table ref="tableSort" v-loading="loading" border :data="tableData">
      <div slot="empty" style="margin: 50px 0">
        <img class="img" src="https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/image/1865665326720536578.png" />
      </div>
      <el-table-column align="center" label="序号">
        <template #default="scope">
          {{
          (queryForm.currentPage - 1) * queryForm.pageSize + scope.$index + 1
        }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="id" label="用户ID" />
      <el-table-column align="center" label="性别">
        <template #default="{ row }">
          <span v-if="row.sex == null">保密</span>
          <el-tag v-if="row.sex == 1" color="#0091FF" disable-transitions effect="dark">
            男
          </el-tag>
          <el-tag v-if="row.sex == 2" color="#FF4D94" disable-transitions effect="dark">
            女
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="电话">
        <template #default="{ row }">
          <span>{{ row.phone }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="注册时间">
        <template #default="{ row }">
          <span v-if="row.createTime">
            {{ timestampToDate(row.createTime) }}
          </span>
        </template>
      </el-table-column>
      <!-- <el-table-column align="center" label="来源渠道（设备）">
        <template #default="{ row }">
          <span>{{ row.deviceNumber }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column align="center" label="订单">
        <template #default="{ row }">
          <span>{{ row.orderCount }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="消费金额">
        <template #default="{ row }">
          <span>{{ row.totalAmount }}</span>
        </template>
      </el-table-column> -->
      <el-table-column align="center" label="标签">
        <template #default="{ row }">
          <span v-if="row.userType == 0"></span>
          <span v-if="row.userType == 1">商户</span>
          <span v-if="row.userType == 2"></span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template #default="{ row }">
          <p>
            <el-link type="primary" icon="el-icon-view" @click="handleView(row)">查看详情</el-link>
          </p>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination v-if="queryForm.total > 20" background :current-page="queryForm.currentPage" :layout="layout"
      :page-size="queryForm.pageSize" :total="queryForm.total" @current-change="handleCurrentChange"
      @size-change="handleSizeChange" />
    <!-- end表格 -->

    <!-- 用户详情弹窗 -->
    <el-dialog title="用户详情" :visible.sync="dialogVisible" width="60%" :before-close="handleClose">
      <div class="user-detail-container">
        <!-- 基本信息 -->
        <div class="info-section">
          <h3 class="section-title">基本信息</h3>
          <div class="info-content">
            <div class="info-row">
              <div class="info-item">
                <span class="label">用户ID:</span>
                <span class="value">{{ userDetail.basicInfo.userId || '' }}</span>
              </div>
              <div class="info-item">
                <span class="label">手机号:</span>
                <span class="value">{{ userDetail.basicInfo.phone || '' }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">性别:</span>
                <span class="value">{{ userDetail.basicInfo.gender }}</span>
              </div>
              <div class="info-item">
                <span class="label">注册时间:</span>
                <span class="value">{{ userDetail.basicInfo.registerTime }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">来源渠道:</span>
                <span class="value">{{ userDetail.basicInfo.channel }}</span>
              </div>
              <div class="info-item">
                <span class="label">标签:</span>
                <div class="tags">
                  <el-tag v-for="(item, index) in userDetail.basicInfo.tags" :key="index" size="mini" class="tag-item">{{ item }}</el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据统计 -->
        <div class="info-section">
          <h3 class="section-title">数据统计</h3>
          <div class="stats-content">
            <div class="stats-item">
              <div class="stats-title">消费订单</div>
              <div class="stats-value">{{ userDetail.statistics.orderCount }}</div>
              <div class="stats-unit">单</div>
            </div>
            <div class="stats-item">
              <div class="stats-title">邀请用户数</div>
              <div class="stats-value">{{ userDetail.statistics.invitedUserCount }}</div>
              <div class="stats-unit">人</div>
            </div>
          </div>
        </div>

        <!-- 订单列表/邀请用户 -->
        <div class="order-section">
          <el-tabs v-model="activeTab">
            <el-tab-pane label="消费订单" name="orders">
              <el-table :data="paginatedOrders" border style="width: 100%">
                <el-table-column prop="orderNo" label="订单号" align="center" />
                <el-table-column label="下单时间" align="center">
                  <template #default="{ row }">
                    {{ row.orderTime }}
                  </template>
                </el-table-column>
                <el-table-column label="金额" align="center">
                  <template #default="{ row }">
                    ￥{{ row.amount }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="100">
                  <template #default="{ row }">
                    <el-button plain type="primary" size="small" @click="handleViewOrder(row)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-pagination
                v-if="userDetail.orderList && userDetail.orderList.length > 5"
                background
                layout="prev, pager, next"
                :total="userDetail.orderList.length"
                :page-size="5"
                :current-page="orderCurrentPage"
                @current-change="handleOrderPageChange"
                class="order-pagination"
              />
            </el-tab-pane>
            <el-tab-pane label="邀请用户" name="invites">
              <el-table :data="paginatedInvites" border style="width: 100%">
                <el-table-column prop="userId" label="用户ID" align="center" />
                <el-table-column label="手机号" align="center">
                  <template #default="{ row }">
                    {{ row.phone }}
                  </template>
                </el-table-column>
                <el-table-column prop="registerTime" label="注册时间" align="center" />
              </el-table>
              <el-pagination
                v-if="userDetail.invitedUsers && userDetail.invitedUsers.length > 5"
                background
                layout="prev, pager, next"
                :total="userDetail.invitedUsers.length"
                :page-size="5"
                :current-page="inviteCurrentPage"
                @current-change="handleInvitePageChange"
                class="order-pagination"
              />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-dialog>

    <!-- 订单详情弹窗 -->
    <el-dialog :center="true" :destroy-on-close="true" title="订单详情" :visible.sync="orderDetailsVisible" width="70%">
      <div v-loading="orderDetailsLoading" class="form-style">
        <div class="form-title">基础信息</div>
        <div class="form-content">
          <div class="form-item">
            <el-form ref="orderDetailsform" :label-position="'left'" label-width="80px">
              <el-form-item label="订单编号">
                <div class="input-refundForm">{{ orderInfo.id }}</div>
              </el-form-item>
              <el-form-item label="设备编号">
                <div class="input-refundForm">{{ orderInfo.deviceId }}</div>
              </el-form-item>
              <el-form-item label="餐品">
                <div class="input-refundForm">{{ orderInfo.dishName }}</div>
              </el-form-item>
              <el-form-item label="数量">
                <div class="input-refundForm">{{ orderInfo.quantity }}</div>
              </el-form-item>
              <el-form-item label="订单价格">
                <div class="input-refundForm">
                  ¥{{ orderInfo.totalPayment }}
                </div>
              </el-form-item>
              <el-form-item label="实际支付">
                <div class="input-refundForm">
                  ¥{{ orderInfo.actualPayment }}
                </div>
              </el-form-item>
              <el-form-item label="支付方式">
                <div class="input-refundForm">微信支付</div>
              </el-form-item>
            </el-form>
          </div>
          <div class="form-item">
            <el-form ref="orderDetailsform" :label-position="'left'" label-width="80px">
              <el-form-item label="口味">
                <div class="input-refundForm">
                  {{ orderInfo.flavor }}
                </div>
              </el-form-item>
              <el-form-item label="是否打包">
                <div class="input-refundForm">
                  {{ orderInfo.packaging == 1 ? '是' : '否' }}
                </div>
              </el-form-item>
              <el-form-item label="创建时间">
                <div class="input-refundForm">
                  {{ timestampToDate(orderInfo.createTime) }}
                </div>
              </el-form-item>
              <el-form-item label="支付时间">
                <div class="input-refundForm">
                  {{ timestampToDate(orderInfo.paymentTime) }}
                </div>
              </el-form-item>
              <el-form-item label="订单状态">
                <div class="input-refundForm">
                  <el-tag v-if="orderInfo.orderStatus == 0" type="primary">
                    待支付
                  </el-tag>
                  <el-tag v-if="orderInfo.orderStatus == 1" type="success">
                    已支付
                  </el-tag>
                  <el-tag v-if="orderInfo.orderStatus == 2" type="success">
                    已分账
                  </el-tag>
                  <el-tag v-if="orderInfo.orderStatus == 3" type="warning">
                    分账失败
                  </el-tag>
                  <el-tag v-if="orderInfo.orderStatus == 4" type="danger">
                    退款中
                  </el-tag>
                  <el-tag v-if="orderInfo.orderStatus == 5" type="success">
                    已退款
                  </el-tag>
                  <el-tag v-if="orderInfo.orderStatus == 6" type="warning">
                    退款失败
                  </el-tag>
                  <el-tag v-if="orderInfo.orderStatus == 7" type="info">
                    已取消
                  </el-tag>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="form-title">用户信息</div>
        <div class="form-content">
          <div class="form-item">
            <el-form ref="orderDetailsform" :inline="true" :label-position="'left'" label-width="80px">
              <el-form-item label="手机号">
                <div class="input-refundForm">{{ orderInfo.phoneNumber }}</div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="orderDetailsVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { userList, userDetails } from '@/api/user'
import { orderDetails } from '@/api/order'
export default {
  name: 'UserIndex',
  components: {},
  data() {
    return {
      queryForm: {
        currentPage: 1, // 页码
        pageSize: 20, // 页数
        operType: 0, // 0查询，1导出
        total: 0, // 总条数
        sex: '', // 性别
        phone: '', // 电话
      },
      loading: false, // 加载状态
      tableData: [], // 列表数据
      layout: 'total, sizes, prev, pager, next, jumper', //   分页布局
      dialogVisible: false, // 用户详情弹窗可见性
      userDetail: {
        basicInfo: {},
        statistics: {},
        orderList: [],
        invitedUsers: [],
      }, // 用户详情数据
      activeTab: 'orders', // 默认激活的标签页
      orderCurrentPage: 1, // 订单当前页
      inviteCurrentPage: 1, // 邀请用户当前页
      // 订单详情相关
      orderDetailsVisible: false, // 订单详情弹窗是否可见
      orderDetailsLoading: false, // 订单详情加载状态
      orderInfo: {}, // 订单详情信息
    }
  },
  computed: {
    // 分页后的订单数据
    paginatedOrders() {
      const start = (this.orderCurrentPage - 1) * 5
      const end = start + 5
      return this.userDetail.orderList ? this.userDetail.orderList.slice(start, end) : []
    },
    // 分页后的邀请用户数据
    paginatedInvites() {
      const start = (this.inviteCurrentPage - 1) * 5
      const end = start + 5
      return this.userDetail.invitedUsers ? this.userDetail.invitedUsers.slice(start, end) : []
    }
  },
  // 在组件创建时执行的钩子函数
  created() {
    // 使用$nextTick确保在DOM更新后执行回调函数
    this.$nextTick(() => {
      // 调用fetchData方法获取数据
      this.fetchData()
    })
  },
  methods: {
    // 时间戳转日期
    timestampToDate(timestamp) {
      // 创建一个新的 Date 对象
      let date = new Date(timestamp)
      // 获取年份、月份（注意：月份是从0开始的）、日期
      let year = date.getFullYear()
      let month = String(date.getMonth() + 1).padStart(2, '0') // 转换为1-12的月份
      let day = String(date.getDate()).padStart(2, '0')
      // 获取小时、分钟、秒数
      let hours = String(date.getHours()).padStart(2, '0')
      let minutes = String(date.getMinutes()).padStart(2, '0')
      let seconds = String(date.getSeconds()).padStart(2, '0')
      // 返回格式化的字符串
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    // 查询
    handleQuery() {
      this.queryForm.currentPage = 1
      this.fetchData()
    },
    // 分页
    handleSizeChange(val) {
      this.queryForm.currentPage = val
      this.fetchData()
    },
    // 分页
    handleCurrentChange(val) {
      this.queryForm.currentPage = val
      this.fetchData()
    },
    // 列表查询
    async fetchData() {
      this.loading = true
      const { code, data } = await userList(this.queryForm)
      if (code == '00000') {
        this.tableData = data.data
        this.queryForm.total = data.paginator.totalRecord
        this.loading = false
      }
    },
    // 查看用户详情
    async handleView(row) {
      this.dialogVisible = true
      this.loading = true
      // 重置分页
      this.orderCurrentPage = 1
      this.inviteCurrentPage = 1
      try {
        const { code, data, msg } = await userDetails({ id: row.id })
        if (code === '00000') {
          this.userDetail = data
        } else {
          this.$message.error(msg || '获取用户详情失败')
        }
      } catch (error) {
        this.$message.error('获取用户详情失败')
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    // 关闭用户详情弹窗
    handleClose() {
      this.dialogVisible = false
      // 重置分页
      this.orderCurrentPage = 1
      this.inviteCurrentPage = 1
    },
    // 订单分页切换
    handleOrderPageChange(page) {
      this.orderCurrentPage = page
    },
    // 邀请用户分页切换
    handleInvitePageChange(page) {
      this.inviteCurrentPage = page
    },
    // 查看订单详情
    handleViewOrder(row) {
      this.orderDetailsVisible = true
      this.orderDetailsLoading = true
      this.viewOrderDetails(row.orderNo)
    },

    // 获取订单详情
    async viewOrderDetails(id) {
      try {
        const { code, data } = await orderDetails({ id })
        if (code === '00000') {
          this.orderInfo = data
        } else {
          this.$message.error('获取订单详情失败')
        }
      } catch (error) {
        this.$message.error('获取订单详情失败')
        console.error(error)
      } finally {
        this.orderDetailsLoading = false
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.link-padding {
  margin-left: 15px;
}

.table-column {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.table-column-price {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 用户详情样式 */
.user-detail-container {
  padding: 0 30px;
}

.section-title {
  position: relative;
  padding-left: 10px;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  border-left: 4px solid #0091FF;
}

.info-section {
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
}

.info-content {
  padding: 0 20px;
}

.info-row {
  display: flex;
  margin-bottom: 20px;
}

.info-item {
  flex: 1;
  display: flex;
  align-items: center;
}

.label {
  width: 80px;
  color: #666;
}

.value {
  color: #333;
  font-weight: 500;
}

.tags {
  display: flex;
  gap: 5px;
}

.stats-content {
  display: flex;
  padding: 20px;
  margin-bottom: 20px;
}

.stats-item {
  flex: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.stats-value {
  font-size: 28px;
  color: #0091FF;
  font-weight: bold;
}

.stats-unit {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.order-section {
  margin-top: 20px;
}

.order-pagination {
  margin-top: 15px;
  text-align: center;
}

/* 订单详情样式 */
.form-style {
  display: flex;
  flex-direction: column;

  .form-title {
    font-size: 14px;
    padding-bottom: 14px;
    color: #909399;
    border-bottom: 1px solid #dcdfe6;
    margin: 20px 0;
  }

  .form-content {
    display: flex;
    margin-top: 10px;
    padding: 0 5px;

    .form-item {
      flex: 1;
    }
  }
}

.input-refundForm {
  color: #606266;
  font-size: 14px;
}
</style>
<style>
.image-slot-error {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f2f6fc;
  font-size: 26px;
}

.el-tag--dark {
  border-color: transparent;
}

/* 弹窗样式修改 */
.el-dialog__header {
  border-bottom: 1px solid #eeeeee;
  padding: 15px 20px;
}

.el-dialog__body {
  padding: 20px 0;
}

.el-tabs__item {
  height: 40px;
  line-height: 40px;
}

.el-tabs__nav-wrap::after {
  height: 1px;
}
</style>
