<template>
  <el-drawer
    :direction="direction"
    size="1100px"
    :title="title"
    :visible.sync="dialogFormVisible"
  >
    <div style="padding: 0px 50px 0 20px">
      <el-form ref="form" label-width="160px" :model="form">
        <el-form-item label="上级类目">
          {{ form.pName }}--{{ form.pid }}
        </el-form-item>
        <el-form-item label="菜单名称：">
          <vab-icon :icon="form.icon" size="40" />
          {{ form.title }}
          <el-tag v-if="form.badge != ''" effect="dark" type="success">
            {{ form.badge }}
          </el-tag>
          <span style="padding-left: 15px">排序：{{ form.sort }}</span>
        </el-form-item>
        <el-form-item label="">
          <el-row>
            <el-col :span="8">
              路由地址(path)： {{ form.path }}
              <div class="shili">
                <p>示例：一级菜单填写：/index</p>
                <p>二级或二级菜单以下填写 view (等同于 /index/view)</p>
              </div>
            </el-col>
            <el-col :span="8">路由名称(name)：{{ form.name }}</el-col>
            <el-col :span="8">
              组件地址component：{{ form.component }}
              <div class="shili">
                <p>示例：一级菜单填写：Layout</p>
                <p>二级或二级菜单以下 @/views/index</p>
              </div>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="">
          <el-row>
            <el-col :span="8">hidden： {{ form.breadcrumbHidden }}</el-col>
            <el-col :span="8">noClosable：{{ form.noClosable }}</el-col>
            <el-col :span="8">noColumn：{{ form.noColumn }}</el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="">
          <el-row>
            <el-col :span="8">
              breadcrumbHidden： {{ form.breadcrumbHidden }}
            </el-col>
            <el-col :span="8">dynamicNewTab：{{ form.noClosable }}</el-col>
            <el-col :span="8">activeMenu：{{ form.noColumn }}</el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <div style="text-align: center">
        <el-button @click="close">关 闭</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
  // import { doRouteTree } from '@/api/router'
  export default {
    name: 'RouteDetail',
    components: {},
    props: {},
    data() {
      return {
        form: {
          pName: '',
          pid: 0,
          title: '',
          icon: 'book-2-fill',
          badge: '',
          path: '',
          name: '',
          component: '',
          sort: 1,
          breadcrumbHidden: false,
          noClosable: false,
          noColumn: false,
          hidden: true,
          activeMenu: '',
          dynamicNewTab: false,
        },
        title: '', //子菜单标题
        clickModel: false,
        direction: 'ltr',
        dialogFormVisible: false,
      }
    },
    watch: {},
    created() {},
    methods: {
      async requestRoleList(id) {
        let { data } = await doRouteTree()
        let name = '主栏目菜单'
        data.forEach((j) => {
          if (j.id == id) {
            name = j.title
          }
          j.children.forEach((k) => {
            if (k.id == id) {
              name = k.title
            }
            k.children.forEach((qq) => {
              if (qq.id == id) {
                name = qq.title
              }
            })
          })
        })
        this.form.pName = name
      },
      show(row) {
        let obj = Object.assign({}, row)
        // console.log('详情菜单：')
        // console.log(obj)
        this.form.id = obj.id
        this.form.pid = obj.pid
        this.form.title = obj.title
        this.form.icon = obj.meta.icon
        this.form.badge = obj.meta.badge
        this.form.path = obj.path
        this.form.name = obj.name
        this.form.component = obj.component
        this.form.sort = obj.sort
        this.form.state = obj.state
        this.form.breadcrumbHidden = obj.meta.breadcrumbHidden
        this.form.noClosable = obj.meta.noClosable
        this.form.noColumn = obj.meta.noColumn
        this.form.hidden = obj.meta.hidden
        this.form.activeMenu = obj.meta.activeMenu
        this.form.dynamicNewTab = obj.meta.dynamicNewTab

        //显示 dialog
        this.requestRoleList(obj.pid)
        this.dialogFormVisible = true
      },
      close() {
        this.dialogFormVisible = false
      },
    },
  }
</script>
<style lang="scss" scoped>
  .image-box {
    .image {
      width: 45px;
      height: 45px;
      margin-right: 10px;
      margin-bottom: 10px;
      cursor: pointer;
      border-radius: 100%;
    }
    .active {
      width: 48px;
      height: 48px;
      border: 3px solid #f34d37;
    }
  }
  .shili {
    padding: 0px 15px;
    margin: 6px 0px 0px 0px;
    background-color: rgb(255, 246, 247);
    border-left: 5px solid rgb(254, 108, 111);
    border-radius: 4px;
    p {
      padding: 0;
      margin: 0;
      font-size: 10px;
      line-height: 20px;
      color: rgb(94, 109, 130);
    }
  }
</style>
