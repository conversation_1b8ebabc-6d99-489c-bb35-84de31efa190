<template>
  <div class="promotion-activity-container">

    <!-- 搜索条件 -->
    <vab-query-form>
      <vab-query-form-left-panel>
        <el-form :inline="true" :model="queryForm" size="mini" label-width="80px">
          <el-form-item label="活动名称">
            <el-input v-model="queryForm.name" placeholder="请输入活动名称" clearable />
          </el-form-item>
          <el-form-item label="活动状态">
            <el-select v-model="queryForm.activityState" placeholder="请选择活动状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="未开始" :value="0" />
              <el-option label="进行中" :value="1" />
              <el-option label="已结束" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetQueryForm">重置</el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel>
        <el-form :inline="true" size="mini" label-width="80px">
          <el-form-item>
            <el-button type="primary" @click="handleAddActivity">新增活动</el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-right-panel>
    </vab-query-form>

    <!-- 活动列表表格 -->
    <el-table ref="tableSort" v-loading="loading" border :data="tableData">
      <div slot="empty" style="margin: 50px 0">
        <img class="img" src="https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/image/1865665326720536578.png" />
      </div>

      <el-table-column align="center" prop="id" label="活动ID" min-width="80" />
      <el-table-column align="center" prop="name" label="活动名称" min-width="120" />

      <el-table-column align="center" prop="activityType" label="活动类型" min-width="100">
        <template #default="{ row }">
          {{ getActivityTypeName(row.activityType) }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="活动时间" min-width="180">
        <template #default="{ row }">
          <div v-if="row.isLongTerm == 0">
            {{ row.startTime }} 至 {{ row.endTime }}
          </div>
          <div v-else>不限时</div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="触发行为" min-width="100">
        <template #default="{ row }">
          <span v-if="row.triggerType == 0">无触发</span>
          <span v-if="row.triggerType == 1">支付完成</span>
          <span v-if="row.triggerType == 2">注册完成</span>
          <span v-if="row.triggerType == 3">首次登录</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="奖励类型" min-width="80">
        <template #default="{ row }">
          <span v-if="row.rewardType == 1">优惠券</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="优惠券配置" min-width="120">
        <template #default="{ row }">
          <span v-if="row.coupons && row.coupons.length > 0">{{ row.coupons[0].name }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="分享率" min-width="80">
        <template #default="{ row }">
          <span>0%</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="裂变系数" min-width="80">
        <template #default="{ row }">
          <span>2.5</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="createTime" label="创建时间" min-width="150" />

      <!-- <el-table-column align="center" prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag v-if="row.status === 0" type="info">未开始</el-tag>
          <el-tag v-if="row.status === 1" type="success">进行中</el-tag>
          <el-tag v-if="row.status === 2" type="warning">已结束</el-tag>
          <el-tag v-if="row.status === 3" type="danger">已取消</el-tag>
        </template>
      </el-table-column> -->

      <el-table-column align="center" label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <div class="operate-style">
            <p>
              <el-link type="primary" icon="el-icon-view" @click="handleView(row)">查看</el-link>
            </p>
            <p v-if="row.status === 0">
              <el-link type="warning" icon="el-icon-edit" @click="handleEdit(row)">编辑</el-link>
            </p>
            <p>
              <el-link type="danger" icon="el-icon-delete" @click="handleDelete(row)">删除</el-link>
            </p>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination v-if="queryForm.total > 0" background :current-page="queryForm.currentPage" :layout="layout"
      :page-size="queryForm.pageSize" :total="queryForm.total" @current-change="handleCurrentChange"
      @size-change="handleSizeChange" />

    <!-- 查看活动详情弹窗 -->
    <el-dialog title="活动详情" :visible.sync="viewDialogVisible" width="50%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="活动ID">{{ currentActivity.id }}</el-descriptions-item>
        <el-descriptions-item label="活动名称">{{ currentActivity.name }}</el-descriptions-item>
        <el-descriptions-item label="活动类型">{{ getActivityTypeName(currentActivity.activityType)
          }}</el-descriptions-item>
        <el-descriptions-item label="活动时间">
          <div v-if="currentActivity.isLongTerm == 0">
            {{ currentActivity.startTime }} 至 {{ currentActivity.endTime }}
          </div>
          <div v-else>不限时</div>
        </el-descriptions-item>
        <el-descriptions-item label="触发行为">
          <span v-if="currentActivity.triggerType == 0">无触发</span>
          <span v-if="currentActivity.triggerType == 1">支付完成</span>
          <span v-if="currentActivity.triggerType == 2">注册完成</span>
          <span v-if="currentActivity.triggerType == 3">首次登录</span>
        </el-descriptions-item>
        <el-descriptions-item label="奖励类型">
          <span v-if="currentActivity.rewardType == 1">优惠券</span>
        </el-descriptions-item>
        <el-descriptions-item label="优惠券配置">
          <span v-if="currentActivity.coupons && currentActivity.coupons.length > 0">
            {{ currentActivity.coupons[0].name }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="分享率">0%</el-descriptions-item>
        <el-descriptions-item label="裂变系数">2.5</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ currentActivity.createTime }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag v-if="currentActivity.status === 0" type="info">未开始</el-tag>
          <el-tag v-if="currentActivity.status === 1" type="success">进行中</el-tag>
          <el-tag v-if="currentActivity.status === 2" type="warning">已结束</el-tag>
          <el-tag v-if="currentActivity.status === 3" type="danger">已取消</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="活动描述" :span="2" v-if="currentActivity.description">{{ currentActivity.description
          }}</el-descriptions-item>
        <el-descriptions-item label="活动规则" :span="2" v-if="currentActivity.activityRules">
          <div v-html="currentActivity.activityRules"></div>
        </el-descriptions-item>
      </el-descriptions>
      <span slot="footer" class="dialog-footer">
        <el-button @click="viewDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 新增/编辑优惠券抽屉 -->
    <el-drawer :title="drawerTitle" :visible.sync="drawerVisible" direction="rtl" size="50%"
      :before-close="handleDrawerClose">
      <div style="padding: 0 20px;">
        <el-form :model="activityForm" :rules="activityRules" label-position="top" ref="activityForm">
          <el-form-item label="活动名称" prop="name">
            <el-input v-model="activityForm.name" placeholder="例: 新用户注册活动" :maxlength="30" />
          </el-form-item>

          <el-form-item label="活动时间" prop="isLongTerm">
            <el-radio-group v-model="activityForm.isLongTerm">
              <el-radio :label="1">不限时</el-radio>
              <el-radio :label="0">指定时间段</el-radio>
            </el-radio-group>
          </el-form-item>

          <template v-if="activityForm.isLongTerm === 0">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker v-model="activityForm.startTime" type="datetime" placeholder="选择日期时间"
                value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%" @change="handleActivityStartTimeChange" />
            </el-form-item>

            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker v-model="activityForm.endTime" type="datetime" placeholder="选择日期时间"
                value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%" :picker-options="activityEndTimeOptions" />
            </el-form-item>
          </template>

          <el-form-item label="触发行为" prop="triggerType">
            <el-select v-model="activityForm.triggerType" placeholder="请选择触发行为" style="width: 100%">
              <el-option label="支付完成" :value="1" />
            </el-select>
            <span style="color: #999; font-size: 12px">当前版本仅支持支付完成触发</span>
          </el-form-item>

          <el-form-item label="奖励类型" prop="rewardType">
            <el-select v-model="activityForm.rewardType" placeholder="请选择奖励类型" style="width: 100%">
              <el-option label="优惠券" :value="1" />
            </el-select>
            <span style="color: #999; font-size: 12px">当前版本仅支持优惠券奖励</span>
          </el-form-item>

          <el-form-item label="优惠券配置" prop="couponTemplateId">
            <el-select v-model="activityForm.couponTemplateId" filterable remote :remote-method="searchCoupon"
              placeholder="请选择优惠券" style="width: 100%">
              <el-option v-for="item in couponList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
            <span v-if="couponList.length === 0" style="color: #FF4949; font-size: 12px">无可用优惠券，请先创建</span>
          </el-form-item>

          <el-form-item style="text-align: center; margin-top: 20px;">
            <el-button @click="drawerVisible = false" style="width: 120px; margin-right: 20px;">取 消</el-button>
            <el-button type="primary" @click="submitActivityForm" style="width: 120px;">{{ isEdit ? '保存修改' : '创建活动'
              }}</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
  </div>
</template>

<script>
// 导入API接口
import { getActivityList, getActivityDetail, createCoupon, getCouponList, createActivity, updateActivity, deleteActivity } from '@/api/marketing'

export default {
  name: 'PromotionActivity',
  components: {},
  data() {
    return {
      loading: false, // 加载状态
      viewDialogVisible: false, // 查看活动详情弹窗
      drawerVisible: false, // 抽屉是否可见
      drawerTitle: '创建优惠券活动', // 抽屉标题
      isEdit: false, // 是否为编辑模式
      currentActivity: {}, // 当前选中的活动
      queryForm: {
        currentPage: 1, // 当前页
        pageSize: 20, // 每页条数
        total: 0, // 总条数
        name: '', // 活动名称
        activityState: '', // 活动状态
        operType: 0 // 操作类型，默认0
      },
      layout: 'total, sizes, prev, pager, next, jumper', // 分页布局
      tableData: [], // 表格数据
      activityTypes: { // 活动类型字典
        0: '普通活动',
        1: '裂变活动'
      },
      activityForm: { // 活动表单
        name: '', // 活动名称
        activityType: 1, // 活动类型：0-普通活动，1-裂变活动
        isLongTerm: 1, // 活动时间类型：0-指定时间段 1-不限时
        startTime: '', // 开始时间
        endTime: '', // 结束时间
        triggerType: 1, // 触发类型：0-无触发，1-支付完成，2-注册完成，3-首次登录
        rewardType: 1, // 奖励类型：1-优惠券
        couponTemplateId: null // 优惠券模板ID
      },
      activityRules: { // 活动表单验证规则
        name: [
          { required: true, message: '请输入活动名称', trigger: 'blur' },
          { min: 2, max: 30, message: '长度在 2 到 30 个字符', trigger: 'blur' }
        ],
        isLongTerm: [
          { required: true, message: '请选择活动时间类型', trigger: 'change' }
        ],
        startTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ],
        triggerType: [
          { required: true, message: '请选择触发行为', trigger: 'change' }
        ],
        rewardType: [
          { required: true, message: '请选择奖励类型', trigger: 'change' }
        ],
        couponTemplateId: [
          { required: true, message: '请选择优惠券', trigger: 'change' }
        ]
      },
      activityEndTimeOptions: { // 活动结束时间选项
        disabledDate: () => false
      },
      couponList: [], // 优惠券列表
      couponForm: { // 优惠券表单
        name: '', // 优惠券名称
        couponType: null, // 优惠券类型 0:金额券 1:折扣券 2无门槛券
        discountValue: null, // 优惠金额/折扣比例
        validPeriodType: 0, // 有效期类型：0-固定日期，1-领取后N天
        minConsumption: null, // 最低消费金额
        receiveCondition: null, // 领取条件 0:无限制 1:仅限新用户
        useScope: null, // 使用范围：0所有设备，1指定设备
        validStartTime: '', // 有效期开始时间
        validEndTime: '', // 有效期结束时间
        totalQuota: 1, // 创建数量
        perLimit: null // 每人领取数量限制
      },
      rules: { // 表单验证规则
        name: [
          { required: true, message: '请输入优惠券名称', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        couponType: [
          { required: true, message: '请选择优惠券类型', trigger: 'change' }
        ],
        discountValue: [
          { required: true, message: '请输入优惠金额/折扣比例', trigger: 'blur' }
        ],
        minConsumption: [
          { pattern: /^\d+$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ],
        receiveCondition: [
          { required: true, message: '请选择领取条件', trigger: 'change' }
        ],
        useScope: [
          { required: true, message: '请选择使用范围', trigger: 'change' }
        ],
        validStartTime: [
          { required: true, message: '请选择有效期开始时间', trigger: 'change' }
        ],
        validEndTime: [
          { required: true, message: '请选择有效期结 束时间', trigger: 'change' }
        ],
        totalQuota: [
          { required: true, message: '请输入创建数量', trigger: 'blur' }
        ],
        perLimit: [
          { required: true, message: '请输入用户领取数量限制', trigger: 'blur' }
        ]
      },
      endTimeOptions: { // 结束时间选项
        disabledDate: () => false
      }
    }
  },
  created() {
    this.fetchData()

    // 监听优惠券类型变化，动态修改验证规则
    this.$watch('couponForm.couponType', (newVal) => {
      // 重置表单验证
      if (this.$refs.couponForm) {
        this.$refs.couponForm.clearValidate()
      }

      if (newVal === 1) { // 金额券
        this.rules.minConsumption = [
          { pattern: /^\d+$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ]
        this.rules.discountValue = [
          { required: true, message: '请输入优惠金额/折扣比例', trigger: 'blur' }
        ]
      } else { // 折扣券
        // 折扣券不需要最低消费金额
        this.rules.minConsumption = []
        // 折扣券需要折扣比例
        this.rules.discountValue = [
          { required: true, message: '请输入折扣比例', trigger: 'blur' }
        ]
      }
    })

    // 监听活动时间类型变化，动态修改验证规则
    this.$watch('activityForm.isLongTerm', (newVal) => {
      // 重置表单验证
      if (this.$refs.activityForm) {
        this.$refs.activityForm.clearValidate()
      }

      if (newVal === 0) { // 不限时
        // 去除开始和结束时间的必填验证
        this.activityRules.startTime = []
        this.activityRules.endTime = []
      } else { // 指定时间段
        // 添加开始和结束时间的必填验证
        this.activityRules.startTime = [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ]
        this.activityRules.endTime = [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ]
      }
    })
  },
  methods: {
    // 获取活动列表
    async fetchData() {
      this.loading = true
      try {
        const { code, data } = await getActivityList(this.queryForm)
        if (code === '00000') {
          this.tableData = data.data
          this.queryForm.total = data.paginator.totalRecord
        }
      } catch (error) {
        console.error('获取活动列表失败', error)
      } finally {
        this.loading = false
      }
    },

    // 分页处理
    handleSizeChange(val) {
      this.queryForm.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.queryForm.currentPage = val
      this.fetchData()
    },

    // 查询
    handleSearch() {
      this.queryForm.currentPage = 1
      this.fetchData()
    },

    // 重置查询表单
    resetQueryForm() {
      this.queryForm.name = ''
      this.queryForm.activityState = ''
      this.handleSearch()
    },

    // 获取活动类型名称
    getActivityTypeName(type) {
      return this.activityTypes[type] || '未知类型'
    },

    // 查看活动详情
    async handleView(row) {
      try {
        const { code, data } = await getActivityDetail({ id: Number(row.id) })
        if (code === '00000') {
          this.currentActivity = data
          this.viewDialogVisible = true
        } else {
          this.$message.error('获取活动详情失败')
        }
      } catch (error) {
        console.error('获取活动详情失败', error)
        this.$message.error('获取活动详情失败')
      }
    },

    // 新增活动
    async handleAddActivity() {
      this.isEdit = false;
      this.drawerTitle = '创建优惠券活动'
      this.resetActivityForm()
      // 获取可用的优惠券列表
      await this.fetchCouponList('')
      this.drawerVisible = true
    },
    // 搜索优惠券
    searchCoupon(query) {
      this.fetchCouponList(query)
    },
    // 获取可用的优惠券列表
    async fetchCouponList(query) {
      try {
        const { code, data } = await getCouponList({
          pageSize: 99,
          currentPage: 1,
          operType: 0,
          scene: 2,
          couponName: query
        }) // 获取未发放的优惠券
        if (code === '00000') {
          this.couponList = data.data || []
        } else {
          this.$message.error('获取优惠券列表失败')
        }
      } catch (error) {
        console.error('获取优惠券列表失败', error)
        this.$message.error('获取优惠券列表失败')
      }
    },

    // 编辑活动
    async handleEdit(row) {
      this.isEdit = true;
      this.drawerTitle = '编辑优惠券活动';

      try {
        // 调用获取活动详情接口
        const { code, data } = await getActivityDetail({ id: Number(row.id) });
        if (code === '00000') {
          // 填充表单数据
          this.activityForm = {
            id: data.id, // 活动ID
            name: data.name, // 活动名称
            activityType: data.activityType, // 活动类型：0-普通活动，1-裂变活动
            isLongTerm: data.isLongTerm, // 活动时间类型：0-指定时间段，1-不限时
            startTime: data.startTime || '', // 开始时间
            endTime: data.endTime || '', // 结束时间
            triggerType: data.triggerType || 1, // 触发类型：0-无触发，1-支付完成，2-注册完成，3-首次登录
            rewardType: data.rewardType || 1, // 奖励类型：1-优惠券
            couponTemplateId: data.coupons[0].id || null // 优惠券模板ID，从优惠券数组的第一个元素获取
          };

          // 获取可用的优惠券列表
          await this.fetchCouponList('');
          this.drawerVisible = true;
        } else {
          this.$message.error('获取活动详情失败');
        }
      } catch (error) {
        console.error('获取活动详情失败', error);
        this.$message.error('获取活动详情失败');
      }
    },

    // 重置活动表单
    resetActivityForm() {
      this.activityForm = {
        name: '',
        isLongTerm: 1,
        activityType: 1,
        startTime: '',
        endTime: '',
        triggerType: 1,
        rewardType: 1,
        couponTemplateId: null
      }
    },

    // 重置优惠券表单
    resetCouponForm() {
      this.couponForm = {
        name: '',
        couponType: null,
        discountValue: null,
        validPeriodType: 0,
        minConsumption: null,
        receiveCondition: null,
        useScope: null,
        validStartTime: '',
        validEndTime: '',
        totalQuota: 1,
        perLimit: null
      }
    },

    // 处理抽屉关闭前的回调
    handleDrawerClose(done) {
      this.$confirm('确认关闭？未保存的数据将会丢失')
        .then(() => {
          done()
        })
        .catch(() => { })
    },

    // 处理活动开始时间变化
    handleActivityStartTimeChange(val) {
      // 如果开始时间被清空，则不设置限制
      if (!val) {
        this.activityEndTimeOptions.disabledDate = () => false;
        return;
      }

      // 设置结束时间必须晚于开始时间
      this.activityEndTimeOptions.disabledDate = time => {
        if (!time) return false;
        return time.getTime() < new Date(val).getTime();
      };

      // 如果结束时间早于开始时间，则清空结束时间
      if (this.activityForm.endTime && new Date(this.activityForm.endTime) < new Date(val)) {
        this.activityForm.endTime = '';
      }
    },

    // 处理开始时间变化
    handleStartTimeChange(val) {
      // 如果开始时间被清空，则不设置限制
      if (!val) {
        this.endTimeOptions.disabledDate = () => false;
        return;
      }

      // 设置结束时间必须晚于开始时间
      this.endTimeOptions.disabledDate = time => {
        if (!time) return false;
        return time.getTime() < new Date(val).getTime();
      };

      // 如果结束时间早于开始时间，则清空结束时间
      if (this.couponForm.validEndTime && new Date(this.couponForm.validEndTime) < new Date(val)) {
        this.couponForm.validEndTime = '';
      }
    },

    // 提交活动表单
    submitActivityForm() {
      this.$refs.activityForm.validate(async valid => {
        if (valid) {
          this.activityForm["couponTemplateIds"] = [this.activityForm.couponTemplateId]
          try {
            let response;

            if (this.isEdit) {
              // 编辑活动
              response = await updateActivity(this.activityForm);
            } else {
              // 创建活动
              response = await createActivity(this.activityForm);
            }

            const { code } = response;
            if (code === '00000') {
              this.$message.success(this.isEdit ? '修改活动成功' : '创建活动成功');
              this.drawerVisible = false;
              this.fetchData();
            }
          } catch (error) {
            this.$message.error(this.isEdit ? '修改活动失败' : '创建活动失败');
          }
        } else {
          return false;
        }
      });
    },

    // 提交表单
    submitForm() {
      this.$refs.couponForm.validate(async valid => {
        if (valid) {
          // 确保数字字段是数字类型
          this.couponForm.discountValue = parseFloat(this.couponForm.discountValue);
          if (this.couponForm.minConsumption !== null && this.couponForm.minConsumption !== '') {
            this.couponForm.minConsumption = parseFloat(this.couponForm.minConsumption);
          } else {
            this.couponForm.minConsumption = 0; // 如果为空，设为0表示无门槛
          }
          this.couponForm.perLimit = parseInt(this.couponForm.perLimit);

          try {
            const { code } = await createCoupon(this.couponForm)
            if (code === '00000') {
              this.$message.success('创建优惠券成功')
              this.drawerVisible = false
              this.fetchData()
            }
          } catch (error) {
            this.$message.error('创建优惠券失败')
          }
        } else {
          return false
        }
      })
    },

    // 删除活动
    handleDelete(row) {
      this.$confirm(`确定要删除 " ${row.name} " 该活动吗？删除后不可恢复`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const { code } = await deleteActivity({ id: Number(row.id) })
          if (code === '00000') {
            this.$message.success('删除活动成功')
            this.fetchData()
          } else {
            this.$message.error('删除活动失败')
          }
        } catch (error) {
          console.error('删除活动失败', error)
          this.$message.error('删除活动失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.promotion-activity-container {
  padding: 10px;

  .page-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      font-size: 18px;
    }
  }

  .el-table {
    margin-top: 20px;
  }

  .dialog-footer {
    margin-top: 20px;
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-descriptions {
    margin-bottom: 20px;
  }
}

.operate-style {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  p {
    margin: 2px 0;
  }
}
</style>
