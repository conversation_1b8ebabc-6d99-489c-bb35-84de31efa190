<template>
  <div class="goods-add-container">
    <el-page-header content="修改设备" @back="goBack" />
    <div style="width: 600px; margin: 30px auto">
      <el-steps :active="active" align-center finish-status="success">
        <el-step description="修改设备信息" title="第一步" />
        <el-step description="操作结果" title="第二步" />
      </el-steps>
    </div>
    <!-- 第一步 -->
    <div v-if="active == 1">
      <el-form
        ref="form"
        class="demo-form"
        :label-position="labelPosition"
        label-width="160px"
        :model="queryForm"
        size="mini"
      >
        <el-row :gutter="0">
          <el-col :span="24">
            <el-form-item label="设备编号" required>
              <el-input
                v-model="queryForm.id"
                :clearable="true"
                disabled
                placeholder="请输入设备编号"
                style="width: 800px"
                type="text"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="所属商户" required>
              <el-select
                v-model="queryForm.merchantId"
                filterable
                placeholder="请选择所属商户"
                style="width: 800px"
              >
                <el-option
                  v-for="item in storeLists"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="运营地区" required>
              <el-cascader
                v-model="areaValue"
                :options="areaData"
                placeholder="请选择运营地区"
                :props="{
                  value: 'areaCode',
                  label: 'areaName',
                  children: 'children',
                }"
                style="width: 800px"
                @change="onChangeArea"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="运营模式" required>
              <el-select
                v-model="queryForm.operationMode"
                filterable
                placeholder="请选择运营模式"
                style="width: 800px"
              >
                <el-option
                  v-for="item in operationModeList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="24">
            <el-form-item label="DTU">
              <el-select
                v-model="queryForm.dtu"
                filterable
                placeholder="请选择DTU"
                style="width: 800px"
              >
                <el-option
                  v-for="item in dutData"
                  :key="item.id"
                  :disabled="item.simCard ? true : false"
                  :label="
                    item.dtuId +
                    (item.simCard
                      ? '（已绑定SIM卡：' + item.simCard + '）'
                      : '')
                  "
                  :value="item.dtuId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="SIM卡号">
              <el-input
                v-model="queryForm.simCard"
                :clearable="true"
                placeholder="请输入SIM卡号"
                style="width: 800px"
                type="text"
              />
            </el-form-item>
          </el-col> -->
          <el-col :span="24">
            <el-form-item label="打包袋">
              <el-switch
                v-model="queryForm.packagingEnabled"
                :active-value="1"
                :inactive-value="0"
              />
              <div style="display: flex; margin-top: 15px" v-if="queryForm.packagingEnabled == 1">
                价格：<el-input
                  placeholder="最低价格：0.1"
                  type="number"
                  style="width: 200px; margin-right: 20px"
                  v-model="queryForm.packagingPrice" />
                库存：<el-input
                  placeholder="最少库存：1"
                  type="number"
                  style="width: 200px"
                  v-model="queryForm.packagingInventory" />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="启用状态">
              <el-switch
                v-model="queryForm.enabled"
                :active-value="'1'"
                :inactive-value="'0'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="标签">
              <el-checkbox-group
                v-model="selectedLabel"
                @change="handleCheckedCitiesChange"
              >
                <el-checkbox
                  v-for="city in labelList"
                  :key="city"
                  :label="city"
                >
                  {{ city }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="分账模板" required>
              <el-radio-group v-model="isSplitTemplateId">
                <el-radio :label="0">不分账</el-radio>
                <el-radio :label="1">选择分账模板</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col v-if="isSplitTemplateId == 1" :span="24">
            <el-form-item label="选择分账模板" required>
              <el-select
                v-model="queryForm.splitTemplateId"
                filterable
                placeholder="请选择分账模板"
                style="width: 800px"
              >
                <el-option
                  v-for="item in shareLists"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="选择餐品" required>
              <div v-if="queryForm.dishList.length > 0" class="food-list">
                <div
                  v-for="(item, index) in queryForm.dishList"
                  :key="index"
                  class="food-li"
                >
                  <i
                    class="el-icon-error delete-info"
                    @click="deleteFood(index)"
                  />
                  <p v-if="item.dishId" class="li-code">
                    餐品编号：{{ item.dishId }}
                  </p>
                  <p v-else class="li-code">餐品编号：未返回</p>
                  <el-image class="li-image" :src="item.dishImage" />
                  <p class="li-name">{{ item.dishName }}</p>
                  <el-input
                    v-model="item.dishPrice"
                    placeholder="请输入餐品价格"
                    type="number"
                  />
                  <el-input
                    v-model="item.stock"
                    placeholder="请输入库存数量"
                    type="number"
                  />
                  <el-input
                    v-model="item.warnStock"
                    placeholder="请输入预警库存数量"
                    type="number"
                  />
                </div>
              </div>
              <el-button
                icon="el-icon-s-unfold"
                type="primary"
                @click="foodDialogVisible = true"
              >
                选择餐品
              </el-button>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="导航地图" required>
              <!-- <el-tag
                v-if="queryForm.longitude && queryForm.latitude"
                style="margin-right: 10px"
              >
                经纬度：{{ queryForm.longitude }}，{{ queryForm.latitude }}
              </el-tag> -->
              <el-input
                v-model="lnglat"
                :clearable="true"
                placeholder="请输入经纬度，例如：120.631706,31.307806，或在地图上选择"
                style="width: 660px; margin-right: 10px"
                type="text"
              />
              <el-button
                icon="el-icon-map-location"
                type="primary"
                @click="openmapPopup(1)"
              >
                {{ queryForm.longitude == '' ? '选择地图' : '重新选择地图' }}
              </el-button>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="投放地址" required>
              <el-input
                v-model="queryForm.address"
                :clearable="true"
                placeholder="请输入投放地址"
                style="width: 800px"
                type="text"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="pay-button-group">
        <el-button type="primary" @click="handleSubmit">确定修改设备</el-button>
      </div>
    </div>
    <!-- 第二步 -->
    <div v-else>
      <div
        style="padding: 80px 0px 0px 0px; margin: 0 auto; text-align: center"
      >
        <el-image
          src="http://admin.image.meijiangwl.com/chenggogn_img.png"
          style="width: 120px; height: 120px"
        />
        <div
          style="padding: 20px 0px 50px 0px; font-size: 20px; color: #333333"
        >
          设备修改成功
        </div>
      </div>
      <div style="padding: 0px 0px 100px 0px; text-align: center">
        <el-button
          style="margin-right: 30px"
          type="info"
          @click="selectClassify"
        >
          关闭修改设备窗口
        </el-button>
      </div>
    </div>
    <el-dialog
      center
      fullscreen
      title="地图"
      :visible.sync="mapDialogVisible"
      @close="closeMapDialog"
    >
      <div class="map-content">
        <div class="map-search-copy">
          <div class="left-input">
            <el-input
              v-model="addressInput"
              class="input-with-select"
              clearable
              placeholder="请输入关键词搜索"
              size="medium"
            >
              <el-select
                v-model="addressSelect"
                placeholder="请选择城市"
                size="medium"
                style="width: 140px"
                slot="prepend"
              >
                <el-option label="全国" value="全国" />
                <el-option label="北京市" value="010" />
                <el-option label="上海市" value="021" />
                <el-option label="天津市" value="022" />
                <el-option label="重庆市" value="023" />
              </el-select>
              <el-button slot="append" @click="PlaceSearch">搜索</el-button>
            </el-input>
          </div>
          <div class="right-lnglat">
            <el-input
              v-model="lnglat"
              class="input-with-select"
              placeholder="请在地图上选中地址"
              readonly
            >
              <template slot="prepend">坐标获取结果</template>
              <el-button slot="append" @click="onCopyLngLat(lnglat)">
                确 定
              </el-button>
            </el-input>
          </div>
        </div>
        <div id="container"></div>
        <div id="panel"></div>
      </div>
    </el-dialog>
    <el-dialog
      center
      title="选择餐品"
      :visible.sync="foodDialogVisible"
      width="80%"
      @close="foodDialogVisible = false"
    >
      <div class="food-content">
        <div class="food-list">
          <div v-for="(item, index) in foodList" :key="index" class="food-li">
            <p class="li-code">餐品编号：{{ item.id }}</p>
            <el-image class="li-image" :src="item.imageUrl" />
            <p class="li-name">{{ item.name }}</p>
            <el-button
              v-if="ifSelectFood(item.id)"
              type="info"
              @click="handleCancelFood(item)"
            >
              取消选中
            </el-button>
            <el-button v-else type="primary" @click="handleSelectFood(item)">
              选中餐品
            </el-button>
          </div>
        </div>
        <el-pagination
          v-if="foodQueryForm.total > 10"
          background
          :current-page="foodQueryForm.currentPage"
          :layout="layout"
          :page-size="foodQueryForm.pageSize"
          :total="foodQueryForm.total"
          @current-change="handleFoodCurrentChange"
          @size-change="handleFoodSizeChange"
        />
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import { mapActions } from 'vuex'
  import { handleActivePath } from '@/utils/routes'
  import AMapLoader from '@amap/amap-jsapi-loader'
  import {
    getAllMerchantIdsAndNames,
    allFoodList,
    deviceDetails,
    editDetails,
  } from '@/api/device'
  import { splitTemplate } from '@/api/business'
  import { areaList } from '@/api/area'
  import { dtuList } from '@/api/iot'
  export default {
    name: 'DeviceEdit',
    components: {},
    data() {
      return {
        mapDialogVisible: false, // 地图弹窗
        operationModeDialogVisible: false, // 选择分账模板弹窗
        foodDialogVisible: false, // 选择餐品模板弹窗
        map: null, // 地图对象
        addressInput: '', // 地址输入框
        addressSelect: '全国', // 地址选择
        lnglat: '', // 经纬度
        active: 1, // 步骤条
        storeLists: [], // 所有商户列表
        labelPosition: 'right', // 标签位置
        selectedLabel: [], // 选中的标签
        labelList: ['机场', '火车站', '酒店', '社区', '医院', '学校', '景区'], // 标签列表
        areaData: [], // 省市区数据
        areaValue: [], // 省市区值
        dutData: [], // DTU列表
        foodList: [], // 菜品列表
        shareLists: [], // 分账列表
        operationModeList: [
          { id: 1, name: '直营' },
          { id: 2, name: '联合运营' },
        ], // 运营模式
        isSplitTemplateId: 0, // 分账模板：0不分账，1分账
        foodQueryForm: {
          currentPage: 1, // 页码
          pageSize: 10, // 页数
          operType: 0, // 状态: 0查询,1导出
          total: 0, // 总页数
        },
        queryForm: {
          deviceId: '', // 设备编号
          merchantId: '', // 所属商户ID
          province: '', // 省
          city: '', // 市
          district: '', // 区
          dishList: [], // 餐品信息
          operationMode: 1, // 运营模式：直营，联合运营
          packagingEnabled: 0, // 打包袋：0不启用，1启用
          packagingPrice: null, // 打包袋价格
          packagingInventory: null, // 打包袋库存
          enabled: '1', // 启用状态：0禁用，1启用
          tags: '', // 标签，多个标签用逗号分隔
          splitTemplateId: '', // 分账模板id
          longitude: '', // 地图坐标经度
          latitude: '', // 地图坐标纬度
          address: '', // 地图详细地址
        },
      }
    },
    created() {
      // 获取省市区数据
      this.getAreaList()
      // 获取餐品列表
      this.getFoodList()
      // 获取所有DTU列表
      // this.dtuAllList()
      // 所有商户下拉列表
      this.storeallLists()
      // 所有分账模板列表
      this.templateIdLists()
      if (this.$route.query.id) {
        let id = this.$route.query.id
        // 获取设备详情
        this.getDeviceDetails(id)
      }
    },
    methods: {
      ...mapActions({
        delVisitedRoute: 'tabs/delVisitedRoute',
      }),
      // 获取设备详情
      async getDeviceDetails(id) {
        const { code, data } = await deviceDetails({ id })
        if (code == '00000') {
          this.queryForm = data
          if (data.dishList == null) {
            this.queryForm.dishList = []
          } else {
            let arr = []
            data.dishList.forEach((item) => {
              arr.push({
                dishId: item.dishId,
                dishName: item.dishName,
                dishImage: item.imageUrl,
                dishPrice: item.price,
                stock: item.stock,
                warnStock: item.warnStock,
              })
            })
            this.queryForm.dishList = arr
          }
          this.lnglat = `${data.longitude},${data.latitude}`
          this.areaValue = [
            String(data.province),
            String(data.city),
            String(data.district),
          ]
          this.queryForm.packagingEnabled = data.packagingEnabled ? 1 : 0
          if (data.enabled) {
            this.queryForm.enabled = '1'
          } else {
            this.queryForm.enabled = '0'
          }
          if (data.splitTemplateId != null) {
            this.isSplitTemplateId = 1
          }
          if (data.tags) {
            this.selectedLabel = data.tags.split(',')
          }
        }
      },
      // 获取省市区数据
      async getAreaList() {
        // 尝试从缓存获取数据
        const cacheKey = 'areaListData';
        const expireKey = 'areaListExpire';

        try {
          // 获取缓存数据
          const cachedData = localStorage.getItem(cacheKey);
          const expireTime = localStorage.getItem(expireKey);

          // 如果缓存存在且未过期，直接使用缓存数据
          if (cachedData && expireTime && Date.now() < parseInt(expireTime)) {
            this.areaData = JSON.parse(cachedData);
            return;
          }

          // 如果缓存不存在或已过期，请求新数据
          const { data } = await areaList({});

          // 更新缓存，设置7天过期时间
          localStorage.setItem(cacheKey, JSON.stringify(data));
          localStorage.setItem(expireKey, String(Date.now() + (7 * 24 * 60 * 60 * 1000)));

          this.areaData = data;
        } catch (error) {
          console.error('获取或缓存省市区数据失败:', error);
          // 出错时尝试从API获取数据
          const { data } = await areaList({});
          this.areaData = data;
        }
      },
      // 省市区事件
      onChangeArea(e) {
        if (e.length > 0) {
          this.queryForm.province = e[0]
          this.queryForm.city = e[1]
          this.queryForm.district = e[2]
        }
      },
      // 分页
      handleFoodSizeChange(val) {
        this.foodQueryForm.currentPage = val
        this.getFoodList()
      },
      // 分页
      handleFoodCurrentChange(val) {
        this.foodQueryForm.currentPage = val
        this.getFoodList()
      },
      // 获取餐品列表
      async getFoodList() {
        let { code, data } = await allFoodList(this.foodQueryForm)
        if (code == '00000') {
          this.foodList = data.data
          this.foodQueryForm.total = data.paginator.totalRecord
        }
      },
      // 判断是否选中
      ifSelectFood(e) {
        let ifSelect = false
        let arr = this.queryForm.dishList
        if (arr.length > 0) {
          arr.forEach((v) => {
            if (e == v.dishId) {
              ifSelect = true
            }
          })
        }
        return ifSelect
      },
      // 选中餐品
      handleSelectFood(e) {
        this.queryForm.dishList.push({
          dishId: e.id,
          dishName: e.name,
          dishImage: e.imageUrl,
          dishPrice: '',
          stock: '',
          warnStock: '',
        })
      },
      // 取消餐品
      handleCancelFood(e) {
        let index = this.queryForm.dishList.findIndex(
          (item) => item.dishId === e.id
        )
        if (index > -1) {
          this.queryForm.dishList.splice(index, 1)
        }
      },
      // 删除餐品
      deleteFood(index) {
        this.queryForm.dishList.splice(index, 1)
      },
      // 获取所有DTU列表
      async dtuAllList() {
        let { code, data } = await dtuList({
          currentPage: 1,
          pageSize: 999,
          operType: 0,
        })
        if (code == '00000') {
          this.dutData = data.data
        }
      },
      // 所有商户下拉列表
      async storeallLists() {
        let { code, data } = await getAllMerchantIdsAndNames({})
        if (code == '00000') {
          this.storeLists = data
        }
      },
      // 所有分账模板列表
      async templateIdLists() {
        let { code, data } = await splitTemplate({
          currentPage: 1,
          pageSize: 999,
          operType: 0,
        })
        if (code == '00000') {
          this.shareLists = data.data
        }
      },
      // 选中标签事件
      handleCheckedCitiesChange(e) {
        this.queryForm.tags = e.join(',')
      },
      // 打开地图弹窗
      openmapPopup(type, value) {
        this.mapDialogVisible = true
        if (type == 1) {
          this.addressInput = this.queryForm.address
        } else if (type == 2) {
          this.addressInput = value
        }
        this.initAMap()
      },
      // 关闭地图弹窗
      closeMapDialog() {
        this.addressInput = ''
        // 清楚地图上的标点
        this.map.clearMap()
        // 清楚旧数据
        document.getElementById('panel').innerHTML = ''
      },
      // 初始化地图
      initAMap() {
        let that = this
        AMapLoader.load({
          key: '5e3eb984bd02ff3da802ec6864f72a7d',
          plugins: ['AMap.PlaceSearch', 'AMap.Scale', 'AMap.ToolBar'],
          AMapUI: {
            version: '1.1',
            plugins: [],
          },
        })
          .then((AMap) => {
            this.map = new AMap.Map('container', {
              resizeEnable: true,
              viewMode: '3D',
              zoom: 18,
              zooms: [10, 22],
              // center: [121.332104, 31.240027],
            })
            that.map.addControl(new AMap.Scale())
            that.map.addControl(new AMap.ToolBar())
            if (that.addressInput) {
              that.PlaceSearch()
            }
            that.map.on('click', (e) => {
              that.lnglat = e.lnglat.getLng() + ',' + e.lnglat.getLat()
              that.queryForm.longitude = e.lnglat.getLng()
              that.queryForm.latitude = e.lnglat.getLat()
            })
          })
          .catch((e) => {
            console.log(e)
          })
      },
      // 搜索地址
      PlaceSearch() {
        var that = this
        // eslint-disable-next-line no-undef
        AMap.service(['AMap.PlaceSearch'], () => {
          //构造地点查询类
          // eslint-disable-next-line no-undef
          var placeSearch = new AMap.PlaceSearch({
            type: '汽车服务|汽车销售|汽车维修|摩托车服务|餐饮服务|购物服务|生活服务|体育休闲服务|医疗保健服务|住宿服务|风景名胜|商务住宅|政府机构及社会团体|科教文化服务|交通设施服务|金融保险服务|公司企业|道路附属设施|地名地址信息|公共设施',
            pageSize: 50, // 单页显示结果条数
            pageIndex: 1, // 页码
            zoom: 18, // 层级
            city: that.addressSelect, // 兴趣点城市
            children: 0, // 展示子节点POI数据
            extensions: 'all', // 返回基本+详细信息
            citylimit: true, //是否强制限制在设置的城市内搜索
            map: that.map, // 展现结果的地图实例
            panel: 'panel', // 结果列表将在此容器中进行展示。
            autoFitView: true, // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
          })
          // 清楚地图上的标点
          that.map.clearMap()
          // 清楚旧数据
          document.getElementById('panel').innerHTML = ''
          // 回到顶部
          document.getElementById('panel').scrollIntoView(true)
          //关键字查询
          placeSearch.search(that.addressInput, function (status, result) {
            console.log(status, result, '查询结果')
          })
          placeSearch.on('listElementClick', (e) => {
            if (e.lastSelected) {
              this.lnglat =
                e.lastSelected.data.location.lng +
                ',' +
                e.lastSelected.data.location.lat
              this.queryForm.longitude = e.lastSelected.data.location.lng
              this.queryForm.latitude = e.lastSelected.data.location.lat
            }
          })
          placeSearch.on('selectChanged', (e) => {
            if (e.selected) {
              this.lnglat =
                e.selected.data.location.lng +
                ',' +
                e.selected.data.location.lat
              this.queryForm.longitude = e.selected.data.location.lng
              this.queryForm.latitude = e.selected.data.location.lat
            }
          })
        })
      },
      // 复制经纬度
      onCopyLngLat(content, message) {
        var aux = document.createElement('input')
        aux.setAttribute('value', content)
        document.body.appendChild(aux)
        aux.select()
        document.execCommand('copy')
        document.body.removeChild(aux)
        if (message == null) {
          this.$message({
            message: '确定成功',
            type: 'success',
          })
          this.mapDialogVisible = false
        } else {
          this.$message.error('复制失败，请手动选中复制')
        }
      },
      // 返回上一页
      async goBack() {
        const detailPath = await handleActivePath(this.$route, true)
        await this.$router.push('/device/list')
        await this.delVisitedRoute(detailPath)
      },
      // 初始化数据
      initData() {
        this.queryForm.deviceNumber = '' // 设备编号
        this.queryForm.merchantId = '' // 所属商户ID
        this.queryForm.province = '' // 省
        this.queryForm.city = '' // 市
        this.queryForm.district = '' // 区
        this.queryForm.operationMode = 1 // 运营模式：1直营，2联合运营，默认1
        this.queryForm.packagingEnabled = 0, // 打包袋：0不启用，1启用
        this.queryForm.packagingPrice = null, // 打包袋价格
        this.queryForm.packagingInventory = null, // 打包袋库存
        this.queryForm.enabled = '1' // 启用状态：0禁用，1启用
        this.queryForm.tags = '' // 标签，多个标签用逗号分隔
        this.queryForm.splitTemplateId = '' // 分账模板id
        this.queryForm.dishList = [] // 在售餐品
        this.queryForm.longitude = '' // 地图坐标经度
        this.queryForm.latitude = '' // 地图坐标纬度
        this.queryForm.address = '' // 地图详细地址
        this.isSplitTemplateId = 0 // 分账模板：0不分账，1分账
        this.selectedLabel = [] // 选中的标签
        this.lnglat = '' // 经纬度
        this.addressInput = '' // 地址输入框
      },
      // 确认添加设备
      async handleSubmit() {
        if (this.queryForm.deviceNumber == '') {
          this.$notify.error({
            title: '温馨提示',
            message: '请输入设备编号',
            offset: 180,
          })
          return false
        } else if (this.queryForm.merchantId == '') {
          this.$notify.error({
            title: '温馨提示',
            message: '请选择所属商户',
            offset: 180,
          })
          return false
        } else if (this.queryForm.province == '') {
          this.$notify.error({
            title: '温馨提示',
            message: '请选择运营地区',
            offset: 180,
          })
          return false
        } else if (
          this.isSplitTemplateId == 1 &&
          this.queryForm.splitTemplateId == ''
        ) {
          this.$notify.error({
            title: '温馨提示',
            message: '请选择分账模板',
            offset: 180,
          })
          return false
        } else if (this.queryForm.dishList.length <= 0) {
          this.$notify.error({
            title: '温馨提示',
            message: '请选择餐品',
            offset: 180,
          })
          return false
        } else if (this.queryForm.latitude == '') {
          this.$notify.error({
            title: '温馨提示',
            message: '请选择导航地图经纬度',
            offset: 180,
          })
          return false
        } else if (this.queryForm.address == '') {
          this.$notify.error({
            title: '温馨提示',
            message: '请输入投放地址',
            offset: 180,
          })
          return false
        } else {
          let flag = true
          let arr = this.queryForm.dishList
          if (arr.length > 0) {
            for (let i = 0; i < arr.length; i++) {
              if (arr[i].dishPrice == '') {
                this.$notify.error({
                  title: '温馨提示',
                  message: `第${i + 1}条餐品价格不能为空`,
                  offset: 180,
                })
                flag = false
                break
              }
              if (arr[i].stock == '') {
                this.$notify.error({
                  title: '温馨提示',
                  message: `第${i + 1}条餐品库存数量不能为空`,
                  offset: 180,
                })
                flag = false
                break
              }
              if (arr[i].warnStock == '') {
                this.$notify.error({
                  title: '温馨提示',
                  message: `第${i + 1}条餐品预警库存数量不能为空`,
                  offset: 180,
                })
                flag = false
                break
              }
            }
          }
          if (!flag) return false
          this.queryForm.packagingPrice = parseFloat(this.queryForm.packagingPrice)
          this.queryForm.packagingInventory = parseInt(this.queryForm.packagingInventory)
          const { code } = await editDetails(this.queryForm)
          if (code == '00000') {
            this.$baseMessage('修改成功', 'success', 'vab-hey-message-success')
            this.active = 2
          }
        }
      },
      //继续添加商户
      async goAddGoods() {
        this.active = 1
        this.initData()
      },
      // 关闭添加窗口，回到设备列表
      async selectClassify() {
        await this.delVisitedRoute(this.$route.path)
        this.$router.push({
          path: '/device/list',
          query: {},
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .map-content {
    position: relative;
    width: 100%;
    min-height: 85vh;
    .map-search-copy {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      margin-bottom: 20px;
      .left-input {
        width: 30%;
      }
      .right-lnglat {
        width: 20%;
      }
    }
  }
  .food-content {
    width: 100%;
  }
  .food-list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    .food-li {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 15px;
      border-radius: 5px;
      margin: 10px;
      position: relative;
      background-color: #F4F4F5;
      box-shadow: 4px 4px 10px 0 rgba(163,178,202,.2);
      .li-code {
        font-size: 14px;
      }
      .li-image {
        width: 200px;
        height: 200px;
      }
      .li-name {
        font-size: 14px;
      }
      .delete-info {
        position: absolute;
        font-size: 30px;
        color: #F56C6C;
        right: -10px;
        top: -10px;
      }
    }
  }
  #container {
    position: absolute;
    width: 100%;
    height: 100%;
    padding: 0px;
    margin: 0px;
  }
  #panel {
    position: absolute;
    top: 52px;
    right: 0px;
    z-index: 999;
    box-sizing: border-box;
    height: 80%;
  }
  .info-desc {
    font-size: 12px;
    color: #999;
  }
  .deleteInfo {
    margin-left: 10px;
  }
  .pay-button-group {
    width: 1000px;
    display: flex;
    margin: 20px 0;
    align-items: center;
    justify-content: center;
  }
  .upload-img-box {
    padding: 0;
    margin: 0;
  }
  .upload-img {
    position: relative;
    box-sizing: border-box;
    display: inline-block;
    width: 234px;
    height: 142px;
    margin: 0 12px 12px 0;
    background-color: #fff;
    border-radius: 6px;
  }
  .upload-img .img {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
  .upload-img .close {
    position: absolute;
    top: -12px;
    right: -12px;
    width: 24px;
    height: 24px;
    cursor: pointer;
  }
  .upload-img-imgs {
    position: relative;
    box-sizing: border-box;
    display: inline-block;
    width: 375px;
    height: 230px;
    margin: 0 12px 12px 0;
    background-color: #fff;
    border-radius: 5px;
  }
  .upload-img-imgs .img {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
  .upload-img-imgs .close {
    position: absolute;
    top: -12px;
    right: -12px;
    width: 24px;
    height: 24px;
    cursor: pointer;
  }
  .textTips {
    padding: 0px 0px;
    font-size: 12px;
    color: #f56c6c;
  }
  .map-content {
    position: relative;
    width: 100%;
    min-height: 85vh;
    .map-search-copy {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      margin-bottom: 20px;
      .left-input {
        width: 30%;
      }
      .right-lnglat {
        width: 20%;
      }
    }
  }
  #container {
    position: absolute;
    width: 100%;
    height: 100%;
    padding: 0px;
    margin: 0px;
  }
  #panel {
    position: absolute;
    top: 56px;
    right: 0px;
    z-index: 999;
    box-sizing: border-box;
    height: calc(100vh - 145px);
    overflow-y: auto;
    background-color: #ffffff;
  }
</style>
