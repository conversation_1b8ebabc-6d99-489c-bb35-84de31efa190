import request from '@/utils/request'

// let List = [
//   {
//     path: '/',
//     name: 'Root',
//     component: 'Layout',
//     meta: {
//       title: '首页',
//       icon: 'home-2-line',
//       breadcrumbHidden: true,
//     },
//     children: [
//       {
//         path: 'index',
//         name: 'Index',
//         component: '@/views/index',
//         meta: {
//           title: '首页',
//           icon: 'home-2-line',
//         },
//       },
//       {
//         path: 'dashboard',
//         name: 'Dashboard',
//         component: '@/views/index/dashboard',
//         meta: {
//           title: '看板',
//           icon: 'dashboard-line',
//         },
//       },
//     ],
//   },
//   {
//     path: '/noColumn',
//     name: 'NoColumn',
//     component: 'Layout',
//     meta: {
//       title: '无分栏',
//       icon: 'delete-column',
//       guard: ['Admin'],
//       breadcrumbHidden: true,
//     },
//     children: [
//       {
//         path: 'deleteColumn',
//         name: 'DeleteColumn',
//         component: '@/views/vab/5',
//         meta: {
//           title: '无分栏',
//           icon: 'delete-column',
//           noColumn: true,
//         },
//       },
//     ],
//   },
//   {
//     path: '/vab',
//     name: 'Vab',
//     component: 'Layout',
//     meta: {
//       title: '系统',
//       icon: 'code-box-line',
//     },
//     children: [
//       {
//         path: 'permission4',
//         name: 'Permission4',
//         component: '@/views/vab/4',
//         meta: {
//           title: '用户管理',
//           icon: 'user-3-line',
//           badge: '99',
//         },
//       },
//       {
//         path: 'permission1',
//         name: 'Permission1',
//         component: '@/views/vab/1',
//         meta: {
//           title: '角色管理',
//           icon: 'user-3-line',
//           badge: '',
//         },
//       },
//       {
//         path: 'permission2',
//         name: 'Permission2',
//         component: '@/views/vab/2',
//         meta: {
//           title: '部门管理',
//           icon: 'user-3-line',
//         },
//       },
//       {
//         path: 'permission',
//         name: 'Permission',
//         component: '@/views/vab/3',
//         meta: {
//           title: '菜单管理',
//           icon: 'user-3-line',
//         },
//       },
//       {
//         path: 'table',
//         name: 'table',
//         meta: {
//           title: '多级菜单',
//           icon: 'user-3-line',
//         },
//         children: [
//           {
//             path: 'detail6',
//             name: 'detail6',
//             component: '@/views/vab/6',
//             meta: {
//               title: '多级菜单表格',
//             },
//           },
//           {
//             path: 'detail7',
//             name: 'Detail7',
//             component: '@/views/vab/7',
//             meta: {
//               hidden: true,
//               title: '多级菜单详情',
//               activeMenu: '/vab/table/detail6',
//               dynamicNewTab: true,
//             },
//           },
//           {
//             path: 'detail8',
//             name: 'Detail8',
//             component: '@/views/vab/8',
//             meta: {
//               title: '多级菜单内容',
//             },
//           },
//         ],
//       },
//       {
//         path: 'http://www.meijiangwl.com',
//         name: 'ExternalLink',
//         meta: {
//           title: '外链',
//           target: '_blank',
//           icon: 'external-link-line',
//         },
//       },
//     ],
//   },
// ]

// export function getList(params) {
//   return { data: { list: List } }
// let result = request({
//   url: '/backend/route/getList',
//   method: 'post',
//   params,
// })
// return result
// return { data: { list: List } }
// }

// 菜单列表
export function getList(data) {
  return request({
    url: '/sys-menu/tree',
    method: 'post',
    data,
  })
}

// 获取菜单列表
export function getMenuList(data) {
  return request({
    url: '/role-menu/getCurrentUserMenuTree',
    method: 'post',
    data,
  })
}

//菜单添加
export function doRouteAdd(data) {
  return request({
    url: '/sys-menu/save',
    method: 'post',
    data,
  })
}

//菜单修改
export function doRouteUpdate(data) {
  return request({
    url: '/sys-menu/update',
    method: 'post',
    data,
  })
}

//菜单删除
export function doRouteDelete(data) {
  return request({
    url: '/backend/route/delete',
    method: 'post',
    data,
  })
}

//下拉菜单列表
export function doRouteSelectList(data) {
  return request({
    url: '/backend/route/select/list',
    method: 'post',
    data,
  })
}
