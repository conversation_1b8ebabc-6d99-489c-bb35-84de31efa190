<template>
  <!-- 商户管理 -->
  <div class="index-container">
    <!-- 查询条件 -->
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form :inline="true" label-width="60px" :model="queryForm">
          <el-form-item label="名称">
            <el-input v-model="queryForm.merchantName" :clearable="true" placeholder="请输入商户名称" style="width: 200px" />
          </el-form-item>
          <el-form-item label="类型">
            <el-select v-model="queryForm.merchantType" placeholder="请选择商户主体类型" style="width: 200px"
              @change="handleQuery">
              <el-option label="全部" value="" />
              <el-option v-for="item in entityTypeList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="地区">
            <el-cascader v-model="areaValue" :options="areaData" placeholder="请选择商户地区" :props="{
              value: 'areaName',
              label: 'areaName',
              children: 'children',
            }" @change="onChangeArea" />
          </el-form-item>
          <el-form-item label="联系人">
            <el-input v-model="queryForm.contactPerson" :clearable="true" placeholder="请输入商户联系人" style="width: 200px" />
          </el-form-item>
          <el-form-item label="电话">
            <el-input v-model="queryForm.contactPhone" :clearable="true" placeholder="请输入商户电话" style="width: 200px" />
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="handleQuery">
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <!-- 按钮 -->
      <vab-query-form-top-panel>
        <el-button icon="el-icon-circle-plus-outline" type="primary" @click="handleAdd">
          新增商户
        </el-button>
      </vab-query-form-top-panel>
      <vab-query-form-top-panel>
        <el-form :inline="true" label-width="60px" :model="query">
          <el-form-item label="ID">
            <el-input v-model="query.id" :clearable="true" placeholder="请输入ID" style="width: 200px" />
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-circle-plus-outline" type="primary" @click="updateMerchantBankInformation">
              更新商户银行信息
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>

      <vab-query-form-top-panel>
        <el-alert :closable="false"
          description="商户名称：深圳市元味食代智能科技有限公司  信用代码：91440300MA5H9U0K6B 银行账户：*********  开户行：中国民生银行股份有限公司深圳科技园支行"
          title="平台商户信息：***************" type="info" />
      </vab-query-form-top-panel>
    </vab-query-form>
    <!-- begin表格 -->
    <el-table ref="tableSort" v-loading="loading" border :data="tableData">
      <div slot="empty" style="margin: 50px auto">
        <img src="~@/assets/index_images/data_empty.png" />
      </div>
      <!-- <el-table-column align="center" label="序号" width="90">
        <template #default="scope">
          {{
            (queryForm.currentPage - 1) * queryForm.pageSize + scope.$index + 1
          }}
        </template>
</el-table-column> -->
      <el-table-column align="center" label="id" min-width="130" prop="id" />
      <el-table-column align="center" label="名称" min-width="120" prop="merchantName" />
      <el-table-column align="center" label="主体类型" min-width="100">
        <template #default="{ row }">
          <div>{{ row.merchantType | entityTypeFun }}</div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="联系人" min-width="100" prop="contactPerson" />
      <el-table-column align="center" label="联系人电话" min-width="100" prop="contactPhone" />
      <el-table-column align="center" label="易票联商户编号" min-width="120">
        <template #default="{ row }">
          <div v-if="row.acqMerid == null">暂无易票联商户编号</div>
          <div v-else>{{ row.acqMerid }}</div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="进件审核状态" min-width="120">
        <template #default="{ row }">
          <div v-if="row.checkStatus == null">暂无状态</div>
          <div v-else>
            <el-tag v-if="row.checkStatus == 0" type="warning">待提交</el-tag>
            <el-tag v-if="row.checkStatus == 1" type="info">审核中</el-tag>
            <el-tag v-if="row.checkStatus == 2" type="success">审核通过</el-tag>
            <el-tag v-if="row.checkStatus == 3" type="danger">
              审核不通过
            </el-tag>
            <el-alert v-if="row.checkStatus == 3 && row.checkErrorMsg" :closable="false"
              :description="row.checkErrorMsg" style="margin-top: 10px" title="失败原因" type="error" />
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="账户状态" min-width="100">
        <template #default="{ row }">
          <div v-if="row.accountStatus == null">暂无状态</div>
          <div v-else>
            <el-tag v-if="row.accountStatus == 0" type="info">未开通</el-tag>
            <el-tag v-if="row.accountStatus == 1" type="success">已开通</el-tag>
            <el-tag v-if="row.accountStatus == 2" type="danger">冻结</el-tag>
            <el-tag v-if="row.accountStatus == 3" type="warning">注销</el-tag>
            <el-tag v-if="row.accountStatus == 4" type="warning">止付</el-tag>
            <el-tag v-if="row.accountStatus == 5" type="warning">禁止入金</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="150">
        <template #default="{ row }">
          <div>
            <el-link type="warning" @click="handleEdit(row)">
              修改资料
            </el-link>
          </div>
          <div>
            <el-link type="success" @click="handleSelect(row)">
              资料详情
            </el-link>
          </div>
          <div v-if="row.checkStatus == 0">
            <el-link type="primary" @click="handleSubmitExamine(row)">
              提交审核
            </el-link>
          </div>
          <!-- <div v-else>
            <el-link type="primary" @click="handleEditExamine(row)">
              更新审核
            </el-link>
          </div> -->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background :current-page="queryForm.currentPage" :layout="layout" :page-size="queryForm.pageSize"
      :total="queryForm.total" @current-change="handleCurrentChange" @size-change="handleSizeChange" />
    <!-- end表格 -->
    <!-- 弹窗 -->
    <el-dialog :title="dialogTitle" top="5vh" :visible.sync="dialogVisible" width="90%">
      <transition v-if="merchantDetails" name="el-fade-in-linear">
        <el-descriptions border :column="4" size="medium" direction="vertical" title="资料详情">
          <el-descriptions-item label="商户名称">
            {{ merchantDetails.merchantName }}
          </el-descriptions-item>
          <el-descriptions-item label="主体类型">
            {{ merchantDetails.merchantType | entityTypeFun }}
          </el-descriptions-item>
          <el-descriptions-item v-if="['2', '4', '9'].includes(merchantDetails.merchantType)" label="是否个人独资企业">
            {{ merchantDetails.isSoleProprietorship ? '是' : '否' }}
          </el-descriptions-item>
          <el-descriptions-item label="联系人姓名">
            {{ merchantDetails.contactPerson || '王大锤' }}
          </el-descriptions-item>
          <el-descriptions-item label="联系人电话">
            {{ merchantDetails.contactPhone }}
          </el-descriptions-item>
          <el-descriptions-item label="身份类型">
            {{ merchantDetails.idUserType == '0' ? '经办人' : '法人' }}
          </el-descriptions-item>
          <el-descriptions-item v-if="merchantDetails.idUserType == '0'" label="经办人证件类型">
            {{ merchantDetails.contactCertType | iDTypeFun }}
          </el-descriptions-item>
          <el-descriptions-item v-if="merchantDetails.idUserType == '0'" label="经办人证件号码">
            {{ merchantDetails.contactCertNo }}
          </el-descriptions-item>
          <el-descriptions-item v-if="merchantDetails.idUserType == '0'" label="经办人证件正面照">
            <el-image fit="contain" :src="merchantDetails.contactPhotoFront"
              :preview-src-list="[merchantDetails.contactPhotoFront]" style="width: 240px; height: 180px" />
          </el-descriptions-item>
          <el-descriptions-item v-if="merchantDetails.idUserType == '0'" label="经办人证件背面照">
            <el-image fit="contain" :src="merchantDetails.contactPhotoBack"
              :preview-src-list="[merchantDetails.contactPhotoBack]" style="width: 240px; height: 150px" />
          </el-descriptions-item>
          <el-descriptions-item v-if="merchantDetails.idUserType == '0'" label="业务办理授权函">
            <el-image fit="contain" :src="merchantDetails.contactBusinessAuth"
              :preview-src-list="[merchantDetails.contactBusinessAuth]" style="width: 240px; height: 150px" />
          </el-descriptions-item>
          <el-descriptions-item v-if="merchantDetails.idUserType == '0'" label="经办人证件姓名">
            {{ merchantDetails.contactPerson }}
          </el-descriptions-item>
          <el-descriptions-item v-if="merchantDetails.idUserType == '0'" label="经办人证件起始有效期">
            {{ merchantDetails.contactCertFrom }}
          </el-descriptions-item>
          <el-descriptions-item v-if="merchantDetails.idUserType == '0'" label="经办人证件截止有效期">
            {{ merchantDetails.contactCertTo }}
          </el-descriptions-item>
          <el-descriptions-item label="法人证件类型">
            {{ merchantDetails.lawyerCertType | iDTypeFun }}
          </el-descriptions-item>
          <el-descriptions-item label="法人证件号码">
            {{ merchantDetails.lawyerCertNo }}
          </el-descriptions-item>
          <el-descriptions-item label="法人证件正面照">
            <el-image fit="contain" :src="merchantDetails.lawyerCertPhotoFront"
              :preview-src-list="[merchantDetails.lawyerCertPhotoFront]" style="width: 240px; height: 150px" />
          </el-descriptions-item>
          <el-descriptions-item label="法人证件背面照">
            <el-image fit="contain" :src="merchantDetails.lawyerCertPhotoBack"
              :preview-src-list="[merchantDetails.lawyerCertPhotoBack]" style="width: 240px; height: 150px" />
          </el-descriptions-item>
          <el-descriptions-item label="法人姓名">
            {{ merchantDetails.lawyerName }}
          </el-descriptions-item>
          <el-descriptions-item label="法人证件起始有效期">
            {{ merchantDetails.certificateFrom }}
          </el-descriptions-item>
          <el-descriptions-item label="法人证件截止有效期">
            {{ merchantDetails.certificateTo }}
          </el-descriptions-item>
          <el-descriptions-item label="法人手机号码">
            {{ merchantDetails.legalPersonPhone }}
          </el-descriptions-item>
          <el-descriptions-item v-if="merchantDetails.lawyerCountry" label="法人证件国籍">
            {{ merchantDetails.lawyerCountry }}
          </el-descriptions-item>
          <el-descriptions-item label="法人证件详细地址">
            {{ merchantDetails.lawyerAddress }}
          </el-descriptions-item>
          <el-descriptions-item label="账户类型">
            {{
              merchantDetails.openAccountType == '1' ? '对公账户' : '法人账户'
            }}
          </el-descriptions-item>
          <el-descriptions-item label="账户名称">
            {{ merchantDetails.licenceAccount }}
          </el-descriptions-item>
          <el-descriptions-item label="账户账号">
            {{ merchantDetails.licenceAccountNo }}
          </el-descriptions-item>
          <el-descriptions-item label="开户银行">
            {{ merchantDetails.licenceOpenBank }}
          </el-descriptions-item>
          <el-descriptions-item label="开户支行" v-if="['2', '4', '9'].includes(merchantDetails.merchantType)">
            {{ merchantDetails.licenceOpenSubBank }}
          </el-descriptions-item>
          <el-descriptions-item label="开户许可证" v-if="['2', '4', '9'].includes(merchantDetails.merchantType)">
            <el-image fit="contain" :src="merchantDetails.openingLicenseAccountPhoto"
              :preview-src-list="[merchantDetails.openingLicenseAccountPhoto]" style="width: 240px; height: 150px" />
          </el-descriptions-item>
          <el-descriptions-item label="结算方式">
            {{ merchantDetails.settleTarget == '1' ? '自动提现' : '手动提现' }}
          </el-descriptions-item>
          <el-descriptions-item label="结算类型">
            {{ merchantDetails.settleAccountType | settlementAccountTypeFun }}
          </el-descriptions-item>
          <el-descriptions-item label="结算账户账号">
            {{ merchantDetails.settleAccountNo }}
          </el-descriptions-item>
          <el-descriptions-item label="结算账户名称">
            {{ merchantDetails.settleAccount }}
          </el-descriptions-item>
          <el-descriptions-item label="结算开户银行">
            {{ merchantDetails.openBank }}
          </el-descriptions-item>
          <el-descriptions-item label="结算开户支行" v-if="['2', '4', '9'].includes(merchantDetails.merchantType)">
            {{ merchantDetails.openSubBank }}
          </el-descriptions-item>
          <el-descriptions-item label="结算开户联行号" v-if="['2', '4', '9'].includes(merchantDetails.merchantType)">
            {{ merchantDetails.openBankCode }}
          </el-descriptions-item>
          <el-descriptions-item label="实际控制人（受益人）姓名">
            {{ merchantDetails.controllerName }}
          </el-descriptions-item>
          <el-descriptions-item label="实际控制人（受益人）证件类型">
            {{ merchantDetails.controllerType | iDTypeFun }}
          </el-descriptions-item>
          <el-descriptions-item label="实际控制人（受益人）证件号码">
            {{ merchantDetails.controllerNo }}
          </el-descriptions-item>
          <el-descriptions-item label="实际控制人（受益人）证件起始有效期">
            {{ merchantDetails.controllerFrom }}
          </el-descriptions-item>
          <el-descriptions-item label="实际控制人（受益人）证件截止有效期">
            {{ merchantDetails.controllerTo }}
          </el-descriptions-item>
          <el-descriptions-item label="实际控制人（受益人）证件国籍">
            {{ merchantDetails.controllerCountry }}
          </el-descriptions-item>
          <el-descriptions-item label="实际控制人（受益人）详细地址">
            {{ merchantDetails.controllerAddress }}
          </el-descriptions-item>
          <el-descriptions-item label="实际控制人（受益人）证件正面照">
            <el-image fit="contain" :src="merchantDetails.controllerPhotoFront"
              :preview-src-list="[merchantDetails.controllerPhotoFront]" style="width: 240px; height: 150px" />
          </el-descriptions-item>
          <el-descriptions-item label="实际控制人（受益人）证件背面照">
            <el-image fit="contain" :src="merchantDetails.controllerPhotoBack"
              :preview-src-list="[merchantDetails.controllerPhotoBack]" style="width: 240px; height: 150px" />
          </el-descriptions-item>
          <el-descriptions-item label="线下收款渠道">
            <el-tag v-for="(item, index) in merchantDetails.businessTypes" :key="index" size="mini"
              :type="randomType()">
              {{ ifBusinessType(item) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </transition>
      <div style="width: 100; margin: 30px 0 0; display: flex; justify-content: center;">
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 子组件 -->
    <table-add ref="add" @fetch-data="handleQuery" />
    <table-edit ref="edit" @fetch-data="handleQuery" />

    <!-- 线下收款业务弹窗 -->
    <el-dialog title="提交审核通过后，商户将开通支付功能，并开通支付渠道，请确认后操作。" :visible.sync="offlinePaymentDialogVisible" width="50%">
      <el-form :model="offlinePaymentForm" label-width="200px">
        <el-form-item label="是否开通线下收款（码牌）：">
          <el-switch v-model="offlinePaymentForm.enabled" active-color="#13ce66" inactive-color="#ff4949"
            @change="handleOfflinePaymentChange" />
        </el-form-item>

        <el-form-item label="开通支付渠道：" v-if="offlinePaymentForm.enabled">
          <el-checkbox-group v-model="offlinePaymentForm.businessTypes">
            <el-checkbox :label="1">微信主扫支付</el-checkbox>
            <el-checkbox :label="2">微信公众号支付</el-checkbox>
            <el-checkbox :label="3">支付宝生活号支付</el-checkbox>
            <el-checkbox :label="4">银联二维码JS支付</el-checkbox>
            <el-checkbox :label="5">账户分账</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="offlinePaymentDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmOfflinePayment">提交审核</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  allBusinessList,
  merchantInfo,
  syncMerchantBank,
  updateMerchantBank,
  queryMerchantInfo,
} from '@/api/business'
import { areaList } from '@/api/area'
import TableAdd from './components/TableAdd.vue'
import TableEdit from './components/TableEdit.vue'
export default {
  name: 'BusinessIndex',
  components: {
    TableAdd,
    TableEdit,
  },
  // 过滤器
  filters: {
    entityTypeFun(e) {
      if (!e) return '暂无主体类型'
      let arr = [
        { id: '1', name: '个体工商户' },
        { id: '2', name: '企业' },
        { id: '3', name: '个人（小微）' },
        { id: '4', name: '政店事业单位' },
        { id: '9', name: '其他组织' },
      ]
      return arr.find((item) => item.id == e).name
    },
    /**
     * 根据传入的证件类型ID，返回对应的证件名称。
     * @param {string} e - 证件类型ID
     * @returns {string} - 对应的证件名称
     */
    iDTypeFun(e) {
      if (!e) return '暂无证件类型'
      let arr = [
        { id: '0', name: '身份证' },
        { id: '1', name: '居住证' },
        { id: '2', name: '签证' },
        { id: '3', name: '护照' },
        { id: '4', name: '户口本' },
        { id: '5', name: '军人证' },
        { id: '6', name: '团员证' },
        { id: '7', name: '党员证' },
        { id: '8', name: '港澳通行证' },
        { id: '9', name: '台胞证' },
        { id: '11', name: '临时身份证' },
        { id: '12', name: '回乡证' },
        { id: '13', name: '营业执照' },
        { id: '14', name: '组织机构代码证' },
        { id: '15', name: '驾驶证' },
        { id: '16', name: '外国人居留证' },
        { id: '99', name: '其他' },
      ]
      return arr.find((item) => item.id == e).name
    },
    /**
     * 根据传入的账户类型ID，返回对应的账户类型名称。
     * @param {string} e - 账户类型ID
     * @returns {string} 账户类型名称
     */
    settlementAccountTypeFun(e) {
      if (!e) return '暂无账户类型'
      let arr = [
        { id: '1', name: '对公账户' },
        { id: '2', name: '法人账户' },
        { id: '3', name: '授权对公' },
        { id: '4', name: '授权对私' },
      ]
      return arr.find((item) => item.id == e).name
    },
  },
  data() {
    return {
      dialogTitle: '', // 弹窗Title
      dialogVisible: false, // 弹窗状态
      merchantDetails: null, // 商户详情
      areaData: [], // 省市区
      areaValue: [], // 省市区值
      query: { // 查询商户审核信息
        id: ''
      },
      entityTypeList: [
        { id: '1', name: '个体工商户' },
        { id: '2', name: '企业' },
        { id: '3', name: '个人（小微）' },
        { id: '4', name: '政店事业单位' },
        { id: '9', name: '其他组织' },
      ],
      queryForm: {
        currentPage: 1, // 页码
        pageSize: 20, // 页数
        operType: 0, // 0查询，1导出
        total: 0, // 总条数
        merchantName: '', // 商户名称
        merchantType: '', // 主体类型:1:个体工商户;2:企业;3:个人(小微);4:政企事业单位;9:其他组织
        province: '', // 省
        city: '', // 市
        district: '', // 区
        contactPhone: '', // 手机号码
        contactPerson: '', // 联系人
      },
      loading: false, // 加载状态
      tableData: [], // 表格数据
      layout: 'total, sizes, prev, pager, next, jumper', //分页配置
      offlinePaymentDialogVisible: false, // 线下收款业务弹窗
      offlinePaymentForm: {
        merchantId: '', // 商户ID
        enabled: false, // 是否开通线下收款
        businessTypes: [] // 开通的业务类型
      },
    }
  },
  beforeMount() { },
  beforeDestroy() { },
  created() {
    // 获取省市区数据
    this.getAreaList()
    this.$nextTick(() => {
      // 获取列表
      this.fetchData()
    })
  },
  methods: {
    // 随机type
    randomType() {
      let fruits = ['primary', 'success', 'warning', 'danger', 'info']
      let randomFruit = fruits[Math.floor(Math.random() * fruits.length)]
      return randomFruit
    },
    // 判断线下收款业务
    ifBusinessType(e) {
      if (!e) return '暂无'
      switch (e) {
        case 1:
          return '微信主扫支付'
        case 2:
          return '微信公众号支付'
        case 3:
          return '支付宝生活号支付'
        case 4:
          return '银联二维码JS支付'
        case 5:
          return '账户分账'
      }
    },
    // 更新商户银行信息
    async updateMerchantBankInformation() {
      if (this.query.id == '') {
        this.$baseMessage('请输入查询 ID', 'error', 'vab-hey-message-error')
        return false
      }
      const { code, data } = await queryMerchantInfo(this.query)
      if (code == '00000') {
        let auditStatusTest = ''
        if (data.auditStatus == '1') {
          auditStatusTest = '待审核'
        } else if (data.auditStatus == '2') {
          auditStatusTest = '审核通过'
        } else if (data.auditStatus == '3') {
          auditStatusTest = '审核不通过'
        }
        this.$alert(
          `审核状态码：${data.auditStatus}(${auditStatusTest})，审核原因：${data.auditMsg}`,
          '查询结果',
          {
            confirmButtonText: '确定',
          }
        )
      }
    },
    // 获取省市区数据
    async getAreaList() {
      try {
        // 尝试从缓存获取数据
        const cachedData = this.getAreaDataFromCache();

        // 如果有缓存数据，直接使用
        if (cachedData) {
          this.areaData = [{ areaCode: 0, areaName: '全部' }, ...cachedData];
          return;
        }

        // 没有缓存或缓存已过期，请求新数据
        const { data } = await areaList({});

        // 缓存数据
        this.saveAreaDataToCache(data);

        // 更新视图
        this.areaData = [{ areaCode: 0, areaName: '全部' }, ...data];
      } catch (error) {
        console.error('获取省市区数据失败:', error);
        // 确保在错误情况下至少有"全部"选项可用
        this.areaData = [{ areaCode: 0, areaName: '全部' }];
      }
    },

    /**
     * 从缓存获取省市区数据
     * @returns {Array|null} 缓存的省市区数据或null
     */
    getAreaDataFromCache() {
      try {
        const cacheKey = 'areaListData';
        const expireKey = 'areaListExpire';

        // 获取缓存数据和过期时间
        const cachedData = localStorage.getItem(cacheKey);
        const expireTime = localStorage.getItem(expireKey);

        // 验证缓存是否存在
        if (!cachedData || !expireTime) {
          return null;
        }

        // 检查是否过期（默认24小时）
        if (Date.now() > parseInt(expireTime)) {
          // 已过期，清除缓存
          localStorage.removeItem(cacheKey);
          localStorage.removeItem(expireKey);
          return null;
        }

        // 解析并返回缓存数据
        return JSON.parse(cachedData);
      } catch (error) {
        // 处理任何可能的错误（如JSON解析错误）
        console.error('读取省市区缓存失败:', error);
        // 清除可能损坏的缓存
        localStorage.removeItem('areaListData');
        localStorage.removeItem('areaListExpire');
        return null;
      }
    },

    /**
     * 将省市区数据保存到缓存中
     * @param {Array} data 要缓存的省市区数据
     */
    saveAreaDataToCache(data) {
      try {
        const cacheKey = 'areaListData';
        const expireKey = 'areaListExpire';
        const expireTime = Date.now() + (7 * 24 * 60 * 60 * 1000); // 7天过期

        // 保存数据和过期时间
        localStorage.setItem(cacheKey, JSON.stringify(data));
        localStorage.setItem(expireKey, expireTime.toString());
      } catch (error) {
        console.error('缓存省市区数据失败:', error);
        // 出错时尝试清除可能部分写入的缓存
        try {
          localStorage.removeItem('areaListData');
          localStorage.removeItem('areaListExpire');
        } catch (e) {
          console.error('清除损坏缓存失败:', e);
        }
      }
    },
    // 省市区事件
    onChangeArea(e) {
      if (e.length > 0) {
        this.queryForm.province = e[0]
        this.queryForm.city = e[1]
        this.queryForm.district = e[2]
      }
    },
    // 商城类型：0全部、1消费区、2返现区、3兑换区
    mallTypeClick() {
      this.tableData = []
      this.queryForm.currentPage = 1
      this.fetchData()
    },
    //商品 全局状态  1上架、2售罄、3下架
    handleClick() {
      this.tableData = []
      this.queryForm.currentPage = 1
      this.fetchData()
    },
    //查询
    handleQuery() {
      this.queryForm.currentPage = 1
      this.fetchData()
    },
    // 分页
    handleSizeChange(val) {
      this.queryForm.currentPage = val
      this.fetchData()
    },
    // 分页
    handleCurrentChange(val) {
      this.queryForm.currentPage = val
      this.fetchData()
    },
    //列表查询
    async fetchData() {
      this.loading = true
      const { data } = await allBusinessList(this.queryForm)
      this.tableData = data.data
      this.queryForm.total = data.paginator.totalRecord
      this.loading = false
    },

    // 添加商户
    handleAdd() {
      this.$refs['add'].show()
    },
    // 编辑商户
    handleEdit(row) {
      //显示编辑商品
      this.$refs['edit'].show(row)
    },
    // 查看商户
    async handleSelect(row) {
      let { code, data } = await merchantInfo({ id: row.id })
      if (code == '00000') {
        this.merchantDetails = data
        this.dialogVisible = true
      }
    },
    // 提交审核
    handleSubmitExamine(e) {
      this.offlinePaymentForm.merchantId = e.id
      this.offlinePaymentDialogVisible = true
    },
    // 更新审核
    handleEditExamine(e) {
      let that = this
      that.$confirm('修改商户信息后，提交更新审核通过会，将修改支付功能，并修改支付渠道，请确认后操作。', '更新审核', {
        confirmButtonText: '确定更新',
        cancelButtonText: '取消',
        callback: async (action) => {
          if (action == 'confirm') {
            let { code } = await updateMerchantBank({ merchantId: e.id })
            if (code == '00000') {
              that.$message({
                type: 'success',
                message: '更新成功'
              })
              that.fetchData()
            }
          }
        }
      })
    },
    // 处理线下收款开关变化
    handleOfflinePaymentChange(value) {
      if (!value) {
        this.offlinePaymentForm.businessTypes = []
      }
    },

    // 确认线下收款业务
    async confirmOfflinePayment() {
      let { code } = await syncMerchantBank(this.offlinePaymentForm)
      if (code == '00000') {
        that.$message({
          type: 'success',
          message: '提交成功'
        })
        // 清空offlinePaymentForm
        this.offlinePaymentForm = {
          merchantId: '',
          enabled: false,
          businessTypes: []
        }
        // 刷新列表
        this.fetchData()
      }
      this.offlinePaymentDialogVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.link-padding {
  margin-left: 15px;
}

.table-column {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.table-column-price {
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
