<template>
  <div class="index-container">
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-button
          icon="el-icon-circle-plus-outline"
          type="primary"
          @click="addTemplate"
        >
          新增模板
        </el-button>
      </vab-query-form-top-panel>
    </vab-query-form>
    <!-- begin表格 -->
    <el-table ref="tableSort" v-loading="loading" border :data="tableData">
      <div slot="empty" style="margin: 50px auto">
        <img src="~@/assets/index_images/data_empty.png" />
      </div>
      <el-table-column align="center" label="序号" width="190">
        <template #default="scope">
          {{
            (queryForm.currentPage - 1) * queryForm.pageSize + scope.$index + 1
          }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="模板ID" prop="id" />
      <el-table-column align="center" label="模板名称">
        <template #default="{ row }">
          <div>{{ row.name }}</div>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="200">
        <template #default="{ row }">
          <div>
            <el-link type="primary" @click="handleDetails(row)">
              模板详情
            </el-link>
          </div>
          <div>
            <el-link type="primary" @click="handleEdit(row)">修改模板</el-link>
          </div>
          <div>
            <el-link type="primary" @click="handleDelete(row)">
              删除模板
            </el-link>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="queryForm.currentPage"
      :layout="layout"
      :page-size="queryForm.pageSize"
      :total="queryForm.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <el-dialog title="模板详情" :visible.sync="dialogDetailsVisible">
      <div class="cents-list">
        <el-card class="cents-li">
          <div slot="header" class="card-header">
            <span>模板名称：{{ templateName }}</span>
            <!-- <el-button icon="el-icon-delete-solid" type="text">删除</el-button> -->
          </div>
          <div>
            <el-table :data="templateDetails">
              <el-table-column align="center" label="分账商户">
                <template #default="{ row }">
                  {{ row.merchantName }}
                </template>
              </el-table-column>
              <el-table-column align="center" label="分账比例">
                <template #default="{ row }">{{ row.ratio }}%</template>
              </el-table-column>
              <el-table-column align="center" label="是否承担手续费">
                <template #default="{ row }">
                  <el-tag v-if="row.ifCharge == 1" type="success">是</el-tag>
                  <el-tag v-else type="info">否</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
        <div @click="dialogDetailsVisible = false">
          <el-button plain type="info">取 消</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 子组件 -->
    <table-edit ref="edit" @fetch-data="handleQuery" />
    <table-add ref="add" @fetch-data="handleQuery" />
  </div>
</template>

<script>
  import { splitTemplate, templateInfo, deleteTemplate } from '@/api/business'
  import TableEdit from './components/TableEdit.vue'
  import TableAdd from './components/TableAdd.vue'
  export default {
    name: 'CentsIndex',
    components: {
      TableEdit,
      TableAdd,
    },
    data() {
      return {
        dialogDetailsVisible: false, // 模板详情弹窗
        tableData: [], // 列表数据
        queryForm: {
          currentPage: 1, // 页码
          pageSize: 12, // 页数
          operType: 0, // 0查询，1导出
          total: 0, // 总条数
        },
        templateName: '', // 模板名称
        templateDetails: [], // 模板详情
        loading: false, // 加载状态
        layout: 'total, sizes, prev, pager, next, jumper', //分页配置
      }
    },
    created() {
      this.$nextTick(() => {
        // 获取列表
        this.fetchData()
      })
    },
    methods: {
      // 新增模板
      addTemplate() {
        this.$refs['add'].show()
      },
      //查询
      handleQuery() {
        this.queryForm.currentPage = 1
        this.fetchData()
      },
      // 分页
      handleSizeChange(val) {
        this.queryForm.currentPage = val
        this.fetchData()
      },
      // 分页
      handleCurrentChange(val) {
        this.queryForm.currentPage = val
        this.fetchData()
      },
      // 列表查询
      async fetchData() {
        this.loading = true
        const { data } = await splitTemplate(this.queryForm)
        this.tableData = data.data
        this.queryForm.total = data.paginator.totalRecord
        this.loading = false
      },

      // 查看详情
      async handleDetails(e) {
        let { code, data } = await templateInfo({
          id: e.id,
        })
        if (code == '00000') {
          this.templateName = e.name
          this.templateDetails = data.details
          this.dialogDetailsVisible = true
        }
      },
      // 编辑商户
      async handleEdit(e) {
        let { code, data } = await templateInfo({
          id: e.id,
        })
        if (code == '00000') {
          this.$refs['edit'].show(data)
        }
      },
      // 删除模板
      handleDelete(e) {
        this.$confirm(`确定删除 “${e.name}“ 该模板吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          const { code } = await deleteTemplate({
            id: e.id,
          })
          if (code == '00000') {
            this.$message.success('删除成功')
            this.handleQuery()
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .cents-list {
    width: 100;
    display: flex;
    align-items: center;
    flex-direction: column;
    .cents-li {
      width: 100%;
      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
  }
</style>
